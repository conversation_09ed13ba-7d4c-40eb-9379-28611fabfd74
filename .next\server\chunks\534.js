exports.id=534,exports.ids=[534],exports.modules={12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14012:(e,t,r)=>{Promise.resolve().then(r.bind(r,69500)),Promise.resolve().then(r.bind(r,29131)),Promise.resolve().then(r.bind(r,19864))},14138:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});var o=r(60687),n=r(10218);function s({children:e,...t}){return(0,o.jsx)(n.N,{...t,children:e})}},16189:(e,t,r)=>{"use strict";var o=r(65773);r.o(o,"useRouter")&&r.d(t,{useRouter:function(){return o.useRouter}})},19864:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>n});var o=r(12907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx","SettingsProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\SettingsContext.tsx","useSettings")},22362:(e,t,r)=>{"use strict";r.d(t,{SettingsProvider:()=>a,t:()=>d});var o=r(60687),n=r(43210);let s=(0,n.createContext)(void 0),i={store:{name:"Revantad Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",email:"<EMAIL>",website:"https://revantadstore.com",currency:"PHP",timezone:"Asia/Manila",businessHours:{open:"06:00",close:"22:00"},operatingDays:["monday","tuesday","wednesday","thursday","friday","saturday"],businessRegistration:{registrationNumber:"REG-2024-001",taxId:"TAX-*********",businessType:"Retail",registrationDate:"2024-01-01"},locations:[{id:1,name:"Main Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",isMain:!0}],branding:{logo:null,primaryColor:"#22c55e",secondaryColor:"#facc15",slogan:"Your Neighborhood Store"}},profile:{firstName:"Admin",lastName:"User",email:"<EMAIL>",phone:"+63 ************",role:"Store Owner",avatar:null,bio:"Experienced store owner managing Revantad Store operations.",dateOfBirth:"1990-01-01",address:"123 Barangay Street, Manila, Philippines",emergencyContact:{name:"Emergency Contact",phone:"+63 ************",relationship:"Family"},preferences:{language:"en",timezone:"Asia/Manila",dateFormat:"MM/DD/YYYY",numberFormat:"en-US"}},notifications:{lowStock:!0,newDebt:!0,paymentReceived:!0,dailyReport:!1,weeklyReport:!0,emailNotifications:!0,smsNotifications:!1,pushNotifications:!0,channels:{email:"<EMAIL>",sms:"+63 ************",webhook:""},customRules:[{id:1,name:"Critical Stock Alert",condition:"stock < 5",action:"email + sms",enabled:!0}],templates:{lowStock:"Product {{productName}} is running low ({{currentStock}} remaining)",newDebt:"New debt recorded for {{customerName}}: ₱{{amount}}",paymentReceived:"Payment received from {{customerName}}: ₱{{amount}}"}},security:{twoFactorAuth:!1,sessionTimeout:"30",passwordExpiry:"90",loginAttempts:"5",currentPassword:"",newPassword:"",confirmPassword:"",apiKeys:[{id:1,name:"Main API Key",key:"sk_live_***************",created:"2024-01-01",lastUsed:"2024-01-20",permissions:["read","write"]}],loginHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",ip:"***********",device:"Chrome on Windows",location:"Manila, Philippines",success:!0}],passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSymbols:!0}},appearance:{theme:"light",language:"en",dateFormat:"MM/DD/YYYY",numberFormat:"en-US",colorScheme:{primary:"#22c55e",secondary:"#facc15",accent:"#3b82f6",background:"#ffffff",surface:"#f8fafc"},layout:{sidebarPosition:"left",density:"comfortable",showAnimations:!0,compactMode:!1},typography:{fontFamily:"Inter",fontSize:"medium",fontWeight:"normal"}},backup:{autoBackup:!0,backupFrequency:"daily",retentionDays:"30",lastBackup:"2024-01-20T10:30:00Z",cloudStorage:{provider:"local",bucket:"",accessKey:"",secretKey:""},backupHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",size:"2.5 MB",status:"completed",type:"automatic"},{id:2,timestamp:"2024-01-19T10:30:00Z",size:"2.4 MB",status:"completed",type:"automatic"}],verification:{enabled:!0,lastVerified:"2024-01-20T10:35:00Z",status:"verified"}}};function a({children:e}){let[t,r]=(0,n.useState)(i),[a,d]=(0,n.useState)(!1),[c,l]=(0,n.useState)(!1),m=async()=>{d(!0);try{await new Promise(e=>setTimeout(e,1e3)),localStorage.setItem("revantad-settings",JSON.stringify(t)),l(!1),"dark"===t.appearance.theme?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),console.log("Settings saved successfully:",t)}catch(e){throw console.error("Error saving settings:",e),e}finally{d(!1)}};return(0,o.jsx)(s.Provider,{value:{settings:t,updateSettings:(e,t)=>{r(r=>({...r,[e]:{...r[e],...t}})),l(!0)},saveSettings:m,resetSettings:e=>{e?r(t=>({...t,[e]:i[e]})):r(i),l(!0)},isLoading:a,hasUnsavedChanges:c},children:e})}function d(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},27107:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>n});var o=r(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx","useAuth");let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\contexts\\AuthContext.tsx","AuthProvider")},41179:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},41550:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},46001:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7",key:"6c3vgh"}]])},61135:()=>{},63213:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>a});var o=r(60687),n=r(43210);let s=(0,n.createContext)(void 0);function i(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function a({children:e}){let[t,r]=(0,n.useState)(null),[i,a]=(0,n.useState)(!0),d=async(e,t)=>{a(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"!==e||"admin123"!==t)return a(!1),!1;{let e={id:"1",email:"<EMAIL>",name:"Admin User",role:"Store Owner"};return r(e),localStorage.setItem("revantad_user",JSON.stringify(e)),a(!1),!0}}catch(e){return console.error("Login error:",e),a(!1),!1}};return(0,o.jsx)(s.Provider,{value:{user:t,login:d,logout:()=>{r(null),localStorage.removeItem("revantad_user")},isLoading:i,isAuthenticated:!!t},children:e})}},69500:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\components\\ThemeProvider.tsx","ThemeProvider")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var o=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72156:(e,t,r)=>{Promise.resolve().then(r.bind(r,14138)),Promise.resolve().then(r.bind(r,63213)),Promise.resolve().then(r.bind(r,22362))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>m});var o=r(37413),n=r(35759),s=r.n(n),i=r(12552),a=r.n(i);r(61135);var d=r(69500),c=r(29131),l=r(19864);let m={title:"Revantad Store - Professional Admin Dashboard",description:"Modern admin dashboard for managing your Revantad Store with product lists, customer debt tracking, and business analytics",keywords:"sari-sari store, admin dashboard, product management, customer debt, business analytics, Philippines"};function u({children:e}){return(0,o.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,o.jsx)("body",{className:`${s().variable} ${a().variable} antialiased`,children:(0,o.jsx)(d.ThemeProvider,{attribute:"class",defaultTheme:"light",enableSystem:!1,storageKey:"revantad-theme",themes:["light","dark"],children:(0,o.jsx)(c.AuthProvider,{children:(0,o.jsx)(l.SettingsProvider,{children:e})})})})})}}};