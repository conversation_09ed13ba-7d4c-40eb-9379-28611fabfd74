(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{283:(e,a,t)=>{"use strict";t.d(a,{A:()=>i,AuthProvider:()=>n});var r=t(5155),s=t(2115);let l=(0,s.createContext)(void 0);function i(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function n(e){let{children:a}=e,[t,i]=(0,s.useState)(null),[n,d]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=localStorage.getItem("revantad_user");if(e)try{let a=JSON.parse(e);i(a)}catch(e){console.error("Error parsing saved user data:",e),localStorage.removeItem("revantad_user")}d(!1)},[]);let o=async(e,a)=>{d(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"!==e||"admin123"!==a)return d(!1),!1;{let e={id:"1",email:"<EMAIL>",name:"Admin User",role:"Store Owner"};return i(e),localStorage.setItem("revantad_user",JSON.stringify(e)),d(!1),!0}}catch(e){return console.error("Login error:",e),d(!1),!1}};return(0,r.jsx)(l.Provider,{value:{user:t,login:o,logout:()=>{i(null),localStorage.removeItem("revantad_user")},isLoading:n,isAuthenticated:!!t},children:a})}},408:(e,a,t)=>{"use strict";t.d(a,{SettingsProvider:()=>n,t:()=>d});var r=t(5155),s=t(2115);let l=(0,s.createContext)(void 0),i={store:{name:"Revantad Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",email:"<EMAIL>",website:"https://revantadstore.com",currency:"PHP",timezone:"Asia/Manila",businessHours:{open:"06:00",close:"22:00"},operatingDays:["monday","tuesday","wednesday","thursday","friday","saturday"],businessRegistration:{registrationNumber:"REG-2024-001",taxId:"TAX-*********",businessType:"Retail",registrationDate:"2024-01-01"},locations:[{id:1,name:"Main Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",isMain:!0}],branding:{logo:null,primaryColor:"#22c55e",secondaryColor:"#facc15",slogan:"Your Neighborhood Store"}},profile:{firstName:"Admin",lastName:"User",email:"<EMAIL>",phone:"+63 ************",role:"Store Owner",avatar:null,bio:"Experienced store owner managing Revantad Store operations.",dateOfBirth:"1990-01-01",address:"123 Barangay Street, Manila, Philippines",emergencyContact:{name:"Emergency Contact",phone:"+63 ************",relationship:"Family"},preferences:{language:"en",timezone:"Asia/Manila",dateFormat:"MM/DD/YYYY",numberFormat:"en-US"}},notifications:{lowStock:!0,newDebt:!0,paymentReceived:!0,dailyReport:!1,weeklyReport:!0,emailNotifications:!0,smsNotifications:!1,pushNotifications:!0,channels:{email:"<EMAIL>",sms:"+63 ************",webhook:""},customRules:[{id:1,name:"Critical Stock Alert",condition:"stock < 5",action:"email + sms",enabled:!0}],templates:{lowStock:"Product {{productName}} is running low ({{currentStock}} remaining)",newDebt:"New debt recorded for {{customerName}}: ₱{{amount}}",paymentReceived:"Payment received from {{customerName}}: ₱{{amount}}"}},security:{twoFactorAuth:!1,sessionTimeout:"30",passwordExpiry:"90",loginAttempts:"5",currentPassword:"",newPassword:"",confirmPassword:"",apiKeys:[{id:1,name:"Main API Key",key:"sk_live_***************",created:"2024-01-01",lastUsed:"2024-01-20",permissions:["read","write"]}],loginHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",ip:"***********",device:"Chrome on Windows",location:"Manila, Philippines",success:!0}],passwordPolicy:{minLength:8,requireUppercase:!0,requireLowercase:!0,requireNumbers:!0,requireSymbols:!0}},appearance:{theme:"light",language:"en",dateFormat:"MM/DD/YYYY",numberFormat:"en-US",colorScheme:{primary:"#22c55e",secondary:"#facc15",accent:"#3b82f6",background:"#ffffff",surface:"#f8fafc"},layout:{sidebarPosition:"left",density:"comfortable",showAnimations:!0,compactMode:!1},typography:{fontFamily:"Inter",fontSize:"medium",fontWeight:"normal"}},backup:{autoBackup:!0,backupFrequency:"daily",retentionDays:"30",lastBackup:"2024-01-20T10:30:00Z",cloudStorage:{provider:"local",bucket:"",accessKey:"",secretKey:""},backupHistory:[{id:1,timestamp:"2024-01-20T10:30:00Z",size:"2.5 MB",status:"completed",type:"automatic"},{id:2,timestamp:"2024-01-19T10:30:00Z",size:"2.4 MB",status:"completed",type:"automatic"}],verification:{enabled:!0,lastVerified:"2024-01-20T10:35:00Z",status:"verified"}}};function n(e){let{children:a}=e,[t,n]=(0,s.useState)(i),[d,o]=(0,s.useState)(!1),[c,x]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=localStorage.getItem("revantad-settings");if(e)try{let a=JSON.parse(e);n({...i,...a})}catch(e){console.error("Error loading settings:",e)}},[]);let m=async()=>{o(!0);try{await new Promise(e=>setTimeout(e,1e3)),localStorage.setItem("revantad-settings",JSON.stringify(t)),x(!1),"dark"===t.appearance.theme?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),console.log("Settings saved successfully:",t)}catch(e){throw console.error("Error saving settings:",e),e}finally{o(!1)}};return(0,r.jsx)(l.Provider,{value:{settings:t,updateSettings:(e,a)=>{n(t=>({...t,[e]:{...t[e],...a}})),x(!0)},saveSettings:m,resetSettings:e=>{e?n(a=>({...a,[e]:i[e]})):n(i),x(!0)},isLoading:d,hasUnsavedChanges:c},children:a})}function d(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},2409:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>aR});var r=t(5155),s=t(1362),l=t(2115),i=t(7340),n=t(7108),d=t(7580),o=t(7213),c=t(7924),x=t(2098),m=t(3509),g=t(1007),u=t(4835),h=t(6874),p=t.n(h),b=t(283);function y(e){let{activeSection:a,setActiveSection:t}=e,[h,y]=(0,l.useState)(""),{setTheme:f,resolvedTheme:j}=(0,s.D)(),[v,k]=(0,l.useState)(!1),[N,w]=(0,l.useState)(!1),{user:A,logout:C}=(0,b.A)();(0,l.useEffect)(()=>{w(!0)},[]);let S=[{id:"dashboard",label:"Home Dashboard",icon:i.A,tooltip:"Dashboard Overview"},{id:"products",label:"Product Lists",icon:n.A,tooltip:"Manage Products"},{id:"debts",label:"Customer Debts",icon:d.A,tooltip:"Customer Debt Management"},{id:"family-gallery",label:"Family Gallery",icon:o.A,tooltip:"Family Photos & Memories"}];return(0,r.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300",style:{backgroundColor:"dark"===j?"#111827":"#ffffff",borderColor:"dark"===j?"#374151":"#e5e7eb"},children:(0,r.jsxs)("div",{className:"grid grid-cols-3 items-center h-16 px-3 sm:px-4 lg:px-6 max-w-full overflow-hidden gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 w-auto",children:[(0,r.jsxs)(p(),{href:"/landing",className:"flex items-center space-x-2 hover:opacity-80 transition-opacity flex-shrink-0",title:"Return to Front Page",children:[(0,r.jsx)("div",{className:"w-10 h-10 hero-gradient rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:"R"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gradient hidden sm:block",children:"Revantad"})]}),(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault(),console.log("Searching for:",h)},className:"w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search",value:h,onChange:e=>y(e.target.value),className:"w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-slate-700 border-0 rounded-full text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:bg-white dark:focus:bg-slate-600 transition-all duration-200"})]})})]}),(0,r.jsx)("div",{className:"hidden sm:flex items-center justify-center",children:(0,r.jsx)("div",{className:"flex items-center space-x-3 md:space-x-4 lg:space-x-5",children:S.map(e=>{let s=e.icon,l=a===e.id;return(0,r.jsxs)("button",{onClick:()=>t(e.id),className:"relative p-3 md:p-3.5 lg:p-4 rounded-xl transition-all duration-200 group min-w-[48px] md:min-w-[52px] lg:min-w-[56px] hover:scale-[1.05]",style:{backgroundColor:l?"dark"===j?"rgba(34, 197, 94, 0.2)":"rgba(34, 197, 94, 0.1)":"transparent",color:l?"dark"===j?"#4ade80":"#16a34a":"dark"===j?"#cbd5e1":"#374151",boxShadow:l?"0 2px 8px rgba(34, 197, 94, 0.2)":"none"},title:e.tooltip,onMouseEnter:e=>{l||(e.currentTarget.style.backgroundColor="dark"===j?"rgba(71, 85, 105, 0.5)":"rgba(243, 244, 246, 0.8)",e.currentTarget.style.color="dark"===j?"#f1f5f9":"#111827")},onMouseLeave:e=>{l||(e.currentTarget.style.backgroundColor="transparent",e.currentTarget.style.color="dark"===j?"#cbd5e1":"#374151")},children:[(0,r.jsx)(s,{className:"h-5 w-5 md:h-6 md:w-6 mx-auto transition-all duration-200"}),l&&(0,r.jsx)("div",{className:"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-10 md:w-12 lg:w-14 h-1 bg-green-500 rounded-full"}),(0,r.jsxs)("div",{className:"absolute top-full mt-3 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-slate-700 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap pointer-events-none shadow-lg z-10",children:[e.tooltip,(0,r.jsx)("div",{className:"absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 dark:bg-slate-700 rotate-45"})]})]},e.id)})})}),(0,r.jsx)("div",{className:"sm:hidden flex items-center justify-center",children:(0,r.jsx)("button",{onClick:()=>t("dashboard"===a?"products":"dashboard"),className:"p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors",title:"Toggle View",children:"dashboard"===a?(0,r.jsx)(n.A,{className:"h-5 w-5"}):(0,r.jsx)(i.A,{className:"h-5 w-5"})})}),(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:()=>{if(!N)return;let e=document.documentElement;"dark"===j?(e.classList.remove("dark"),f("light")):(e.classList.add("dark"),f("dark"))},className:"p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 flex-shrink-0",title:N?"Switch to ".concat("dark"===j?"light":"dark"," mode (Current: ").concat(j,")"):"Toggle theme",disabled:!N,children:N?"dark"===j?(0,r.jsx)(x.A,{className:"h-5 w-5"}):(0,r.jsx)(m.A,{className:"h-5 w-5"}):(0,r.jsx)("div",{className:"h-5 w-5 bg-gray-400 rounded-full animate-pulse"})}),(0,r.jsxs)("div",{className:"relative flex-shrink-0",children:[(0,r.jsxs)("button",{onClick:()=>k(!v),className:"flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 group",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow",children:(0,r.jsx)(g.A,{className:"h-4 w-4 text-white"})}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 hidden sm:block group-hover:text-gray-900 dark:group-hover:text-white transition-colors",children:(null==A?void 0:A.name)||"Admin"})]}),v&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1",children:[(0,r.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200 dark:border-slate-700",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:(null==A?void 0:A.name)||"Admin User"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:(null==A?void 0:A.email)||"<EMAIL>"})]}),(0,r.jsxs)("button",{onClick:()=>t("settings"),className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Settings"})]}),(0,r.jsxs)("button",{onClick:()=>{C(),window.location.href="/login"},className:"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Logout"})]})]})]})]})]})})}var f=t(5657),j=t(2713),v=t(9676),k=t(9074),N=t(381),w=t(3052),A=t(2355);function C(e){let{activeSection:a,setActiveSection:t}=e,{resolvedTheme:i}=(0,s.D)(),[n,d]=(0,l.useState)(!1),[o,c]=(0,l.useState)(!1);(0,l.useEffect)(()=>{c(!0);let e=localStorage.getItem("sidebar-collapsed");null!==e&&d(JSON.parse(e))},[]),(0,l.useEffect)(()=>{o&&localStorage.setItem("sidebar-collapsed",JSON.stringify(n))},[n,o]);let x=[{id:"ai-support",label:"AI Support",icon:f.A},{id:"api-graphing",label:"API Graphing & Visuals",icon:j.A},{id:"history",label:"History",icon:v.A},{id:"calendar",label:"Calendar",icon:k.A},{id:"settings",label:"Settings",icon:N.A}];return(0,r.jsx)("div",{className:"shadow-xl border-r sticky top-16 h-[calc(100vh-4rem)] transition-all duration-300 ease-in-out ".concat(n?"w-20 min-w-20":"w-80 min-w-80"),style:{backgroundColor:"dark"===i?"#1e293b":"#ffffff",borderColor:"dark"===i?"#334155":"#e5e7eb",borderWidth:"1px",boxShadow:"dark"===i?"0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)":"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)"},children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"sticky top-0 z-20 transition-all duration-300 backdrop-blur-md ".concat(n?"px-3 py-3":"px-6 py-4"),style:{background:"dark"===i?"linear-gradient(135deg, rgba(30, 41, 59, 0.98) 0%, rgba(51, 65, 85, 0.95) 100%)":"linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(249, 250, 251, 0.95) 100%)",borderBottom:"dark"===i?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===i?"0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)":"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},children:[(0,r.jsxs)("div",{className:"flex items-center ".concat(n?"justify-center":"mb-3"),children:[(0,r.jsx)("div",{className:"rounded-lg flex items-center justify-center transition-all duration-300 ".concat(n?"w-10 h-10":"w-8 h-8 mr-3"),style:{background:"dark"===i?"linear-gradient(135deg, #22c55e 0%, #16a34a 100%)":"linear-gradient(135deg, #10b981 0%, #059669 100%)",boxShadow:"0 4px 8px rgba(34, 197, 94, 0.3)"},children:(0,r.jsx)("span",{className:"text-white font-bold ".concat(n?"text-base":"text-sm"),children:"⚡"})}),!n&&(0,r.jsx)("h2",{className:"text-lg font-bold transition-all duration-300 crisp-text",style:{color:"dark"===i?"#f8fafc":"#111827",textShadow:"dark"===i?"0 1px 2px rgba(0, 0, 0, 0.3)":"none"},children:"Additional Tools"})]}),!n&&(0,r.jsx)("p",{className:"text-xs font-medium transition-all duration-300 crisp-text",style:{color:"dark"===i?"#94a3b8":"#64748b",letterSpacing:"0.025em"},children:"Advanced features and utilities"})]}),(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)("button",{onClick:()=>{d(!n)},className:"absolute top-4 right-2 z-30 p-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 group",style:{backgroundColor:"dark"===i?"rgba(34, 197, 94, 0.1)":"rgba(34, 197, 94, 0.08)",border:"dark"===i?"1px solid rgba(34, 197, 94, 0.3)":"1px solid rgba(34, 197, 94, 0.2)",boxShadow:"dark"===i?"0 2px 8px rgba(34, 197, 94, 0.2)":"0 2px 8px rgba(34, 197, 94, 0.15)"},title:n?"Expand Sidebar":"Collapse Sidebar",children:n?(0,r.jsx)(w.A,{className:"w-4 h-4 transition-all duration-200 group-hover:scale-105",style:{color:"dark"===i?"#4ade80":"#16a34a"}}):(0,r.jsx)(A.A,{className:"w-4 h-4 transition-all duration-200 group-hover:scale-105",style:{color:"dark"===i?"#4ade80":"#16a34a"}})}),(0,r.jsx)("div",{className:"absolute inset-0 scroll-fade-top scroll-fade-bottom",children:(0,r.jsx)("nav",{className:"h-full pt-16 pb-6 overflow-y-auto sidebar-nav-scroll transition-all duration-300 space-y-1 ".concat(n?"px-2":"px-4"),children:x.map(e=>{let s=e.icon,l=a===e.id;return(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("button",{onClick:()=>t(e.id),className:"w-full flex items-center text-left transition-all duration-200 group sidebar-nav-item crisp-text relative overflow-hidden ".concat(n?"p-2.5 rounded-lg justify-center":"p-3 rounded-xl"),style:{background:l?"dark"===i?"linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)":"linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%)":"transparent",border:l?"dark"===i?"1px solid rgba(34, 197, 94, 0.4)":"1px solid rgba(34, 197, 94, 0.3)":"1px solid transparent",boxShadow:l?"dark"===i?"0 4px 12px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 4px 12px rgba(34, 197, 94, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)":"none"},onMouseEnter:e=>{l||(e.currentTarget.style.background="dark"===i?"rgba(71, 85, 105, 0.15)":"rgba(243, 244, 246, 0.6)",e.currentTarget.style.border="dark"===i?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.6)")},onMouseLeave:e=>{l||(e.currentTarget.style.background="transparent",e.currentTarget.style.border="1px solid transparent")},children:n?(0,r.jsx)(s,{className:"h-5 w-5 transition-all duration-200 relative z-10",style:{color:l?"dark"===i?"#4ade80":"#16a34a":"dark"===i?"#e2e8f0":"#64748b",filter:l?"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))":"none"}}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"transition-all duration-200 relative p-2 rounded-lg mr-3 overflow-hidden",style:{background:l?"dark"===i?"linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.25) 100%)":"linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)":"dark"===i?"linear-gradient(135deg, rgba(71, 85, 105, 0.5) 0%, rgba(51, 65, 85, 0.4) 100%)":"linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)",boxShadow:l?"dark"===i?"0 2px 8px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 2px 8px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)":"dark"===i?"inset 0 1px 0 rgba(255, 255, 255, 0.05)":"inset 0 1px 0 rgba(255, 255, 255, 0.9)"},children:(0,r.jsx)(s,{className:"h-4 w-4 transition-all duration-200 relative z-10",style:{color:l?"dark"===i?"#4ade80":"#16a34a":"dark"===i?"#e2e8f0":"#64748b",filter:l?"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))":"none"}})}),(0,r.jsx)("div",{className:"flex-1 sidebar-text",children:(0,r.jsx)("h3",{className:"font-medium text-sm transition-colors duration-200 leading-snug",style:{color:l?"dark"===i?"#4ade80":"#16a34a":"dark"===i?"#f8fafc":"#111827",fontWeight:l?"600":"500"},children:e.label})})]})}),n&&(0,r.jsxs)("div",{className:"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 rounded-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-50 whitespace-nowrap",style:{backgroundColor:"dark"===i?"#1e293b":"#ffffff",color:"dark"===i?"#f8fafc":"#111827",border:"dark"===i?"1px solid rgba(148, 163, 184, 0.3)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===i?"0 4px 12px rgba(0, 0, 0, 0.3)":"0 4px 12px rgba(0, 0, 0, 0.15)"},children:[(0,r.jsx)("div",{className:"font-semibold",children:e.label}),(0,r.jsx)("div",{className:"absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0",style:{borderTop:"6px solid transparent",borderBottom:"6px solid transparent",borderRight:"6px solid ".concat("dark"===i?"#1e293b":"#ffffff")}})]})]},e.id)})})})]}),(0,r.jsx)("div",{className:"sticky bottom-0 z-20 transition-all duration-300 backdrop-blur-md ".concat(n?"px-3 py-3":"px-6 py-4"),style:{background:"dark"===i?"linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%)":"linear-gradient(135deg, rgba(249, 250, 251, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%)",borderTop:"dark"===i?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===i?"0 -4px 6px -1px rgba(0, 0, 0, 0.3), 0 -2px 4px -1px rgba(0, 0, 0, 0.2)":"0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)"},children:(0,r.jsx)("div",{className:"text-sm transition-colors duration-300",style:{color:"dark"===i?"#94a3b8":"#64748b"},children:n?(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"rounded-xl flex items-center justify-center relative overflow-hidden w-10 h-10",style:{background:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",boxShadow:"0 4px 8px rgba(59, 130, 246, 0.3)"},children:[(0,r.jsx)("span",{className:"text-white font-bold relative z-10 text-base",children:"R"}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)"}})]})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center mb-2 space-x-3",children:[(0,r.jsxs)("div",{className:"rounded-xl flex items-center justify-center relative overflow-hidden w-8 h-8",style:{background:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",boxShadow:"0 4px 8px rgba(59, 130, 246, 0.3)"},children:[(0,r.jsx)("span",{className:"text-white font-bold relative z-10 text-sm",children:"R"}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)"}})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-bold text-sm transition-colors duration-300 block",style:{color:"dark"===i?"#f8fafc":"#1e293b",textShadow:"dark"===i?"0 1px 2px rgba(0, 0, 0, 0.3)":"none"},children:"Revantad Store"}),(0,r.jsx)("span",{className:"text-xs font-medium",style:{color:"dark"===i?"#64748b":"#94a3b8",letterSpacing:"0.025em"},children:"Professional Business Management"})]})]}),(0,r.jsx)("div",{className:"text-xs font-medium px-3 py-2 rounded-lg",style:{backgroundColor:"dark"===i?"rgba(71, 85, 105, 0.3)":"rgba(243, 244, 246, 0.8)",color:"dark"===i?"#cbd5e1":"#6b7280",border:"dark"===i?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.6)"},children:"Admin Dashboard v2.0"})]})})}),!n&&(0,r.jsx)("div",{className:"absolute bottom-2 left-1/2 transform -translate-x-1/2 opacity-60 pointer-events-none",style:{color:"dark"===i?"#94a3b8":"#64748b"},children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,r.jsx)("div",{className:"text-xs font-medium",children:"Scroll for more"}),(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-1 h-1 rounded-full bg-current animate-pulse"}),(0,r.jsx)("div",{className:"w-1 h-1 rounded-full bg-current animate-pulse",style:{animationDelay:"0.2s"}}),(0,r.jsx)("div",{className:"w-1 h-1 rounded-full bg-current animate-pulse",style:{animationDelay:"0.4s"}})]})]})})]})})}var S=t(5868),D=t(1243),P=t(9397),M=t(4186),T=t(3904),L=t(4870),E=t(8515),R=t(1539),_=t(4616),I=t(1586),z=t(7809),F=t(6785),B=t(3861),U=t(4355);function O(e){let{stats:a,onSectionChange:t}=e,{resolvedTheme:i}=(0,s.D)(),[o,c]=(0,l.useState)(!1),[x,m]=(0,l.useState)(new Date),[g,u]=(0,l.useState)(a);(0,l.useEffect)(()=>{let e=setInterval(()=>{m(new Date)},1e3);return()=>clearInterval(e)},[]),(0,l.useEffect)(()=>{u(a)},[a]);let h=async()=>{c(!0),await new Promise(e=>setTimeout(e,1e3)),c(!1)},p=[{label:"Products in List",value:g.totalProducts,previousValue:Math.max(0,g.totalProducts-Math.floor(5*Math.random())),change:12.5,changeType:"increase",icon:n.A,color:"#3b82f6",bgColor:"dark"===i?"rgba(59, 130, 246, 0.1)":"rgba(59, 130, 246, 0.05)"},{label:"Customer Debts",value:g.totalDebts,previousValue:Math.max(0,g.totalDebts+Math.floor(3*Math.random())),change:-8.3,changeType:"decrease",icon:d.A,color:"#10b981",bgColor:"dark"===i?"rgba(16, 185, 129, 0.1)":"rgba(16, 185, 129, 0.05)"},{label:"Total Debt Amount",value:g.totalDebtAmount,previousValue:g.totalDebtAmount+Math.floor(1e3*Math.random()),change:-15.7,changeType:"decrease",icon:S.A,color:"#f59e0b",bgColor:"dark"===i?"rgba(245, 158, 11, 0.1)":"rgba(245, 158, 11, 0.05)"},{label:"Low Stock Items",value:g.lowStockItems,previousValue:Math.max(0,g.lowStockItems+Math.floor(2*Math.random())),change:-25,changeType:"decrease",icon:D.A,color:"#ef4444",bgColor:"dark"===i?"rgba(239, 68, 68, 0.1)":"rgba(239, 68, 68, 0.05)"}],b=e=>new Intl.NumberFormat("en-PH",{style:"currency",currency:"PHP",minimumFractionDigits:2}).format(e),y=e=>{if(t)switch(e){case"add-product":case"manage-stock":t("products");break;case"record-debt":t("debts");break;case"view-analytics":t("api-graphing");break;case"view-history":t("history")}};return(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsx)("div",{className:"rounded-2xl shadow-lg p-6 border transition-all duration-300",style:{backgroundColor:"dark"===i?"#1e293b":"#ffffff",border:"dark"===i?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===i?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)"},children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold mb-2 flex items-center gap-3",style:{color:"dark"===i?"#f8fafc":"#111827"},children:[(0,r.jsx)(P.A,{className:"h-7 w-7 text-green-500"}),"Dashboard Overview of your Revantad Store"]}),(0,r.jsxs)("p",{className:"text-sm flex items-center gap-2",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:[(0,r.jsx)(M.A,{className:"h-4 w-4"}),"Last updated: ",x.toLocaleTimeString("en-PH",{hour:"2-digit",minute:"2-digit",second:"2-digit"})," • Real-time monitoring active"]})]}),(0,r.jsxs)("button",{onClick:h,disabled:o,className:"flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",style:{backgroundColor:"dark"===i?"rgba(34, 197, 94, 0.1)":"rgba(34, 197, 94, 0.08)",border:"dark"===i?"1px solid rgba(34, 197, 94, 0.3)":"1px solid rgba(34, 197, 94, 0.2)",color:"dark"===i?"#4ade80":"#16a34a"},children:[(0,r.jsx)(T.A,{className:"h-4 w-4 ".concat(o?"animate-spin":"")}),o?"Refreshing...":"Refresh"]})]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:p.map((e,a)=>{let t=e.icon,s="increase"===e.changeType?L.A:E.A,l="increase"===e.changeType;return(0,r.jsxs)("div",{className:"group rounded-2xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] cursor-pointer border",style:{backgroundColor:"dark"===i?"#1e293b":"#ffffff",border:"dark"===i?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===i?"linear-gradient(135deg, #1e293b 0%, ".concat(e.bgColor," 100%)"):"linear-gradient(135deg, #ffffff 0%, ".concat(e.bgColor," 100%)")},children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsx)("div",{className:"p-3 rounded-xl transition-all duration-300 group-hover:scale-110",style:{backgroundColor:e.bgColor,border:"1px solid ".concat(e.color,"20")},children:(0,r.jsx)(t,{className:"h-6 w-6 transition-all duration-300",style:{color:e.color}})}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(s,{className:"h-4 w-4 ".concat(l?"text-green-500":"text-red-500")}),(0,r.jsxs)("span",{className:"text-sm font-semibold ".concat(l?"text-green-500":"text-red-500"),children:[Math.abs(e.change),"%"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium mb-2 transition-colors duration-300",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:e.label}),(0,r.jsx)("p",{className:"text-3xl font-bold mb-1 transition-colors duration-300",style:{color:"dark"===i?"#f8fafc":"#111827"},children:e.label.includes("Amount")?b(e.value):e.value.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs transition-colors duration-300",style:{color:"dark"===i?"#94a3b8":"#9ca3af"},children:["vs ",e.label.includes("Amount")?b(e.previousValue):e.previousValue.toLocaleString()," last period"]})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("div",{className:"h-1 rounded-full overflow-hidden",style:{backgroundColor:"dark"===i?"#334155":"#f1f5f9"},children:(0,r.jsx)("div",{className:"h-full rounded-full transition-all duration-1000 ease-out",style:{backgroundColor:e.color,width:"".concat(Math.min(100,e.value/(e.value+e.previousValue)*100),"%")}})})})]},a)})}),(0,r.jsxs)("div",{className:"rounded-2xl shadow-lg p-6 border transition-all duration-300",style:{backgroundColor:"dark"===i?"#1e293b":"#ffffff",border:"dark"===i?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===i?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(R.A,{className:"h-6 w-6 text-yellow-500"}),(0,r.jsx)("h3",{className:"text-xl font-bold transition-colors duration-300",style:{color:"dark"===i?"#f8fafc":"#111827"},children:"Quick Actions"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("button",{onClick:()=>y("add-product"),className:"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",style:{border:"dark"===i?"1px solid #475569":"1px solid #d1d5db",backgroundColor:"dark"===i?"#334155":"#f9fafb",background:"dark"===i?"linear-gradient(135deg, #334155 0%, #475569 100%)":"linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)"},title:"Navigate to Products section to add new items",children:[(0,r.jsx)("div",{className:"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110",style:{backgroundColor:"rgba(59, 130, 246, 0.1)"},children:(0,r.jsx)(_.A,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsx)("p",{className:"font-semibold mb-1",style:{color:"dark"===i?"#f8fafc":"#111827"},children:"Add Product"}),(0,r.jsx)("p",{className:"text-xs text-center",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:"Add new item to inventory"})]}),(0,r.jsxs)("button",{onClick:()=>y("record-debt"),className:"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",style:{border:"dark"===i?"1px solid #475569":"1px solid #d1d5db",backgroundColor:"dark"===i?"#334155":"#f9fafb",background:"dark"===i?"linear-gradient(135deg, #334155 0%, #475569 100%)":"linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)"},title:"Navigate to Debts section to record customer debt",children:[(0,r.jsx)("div",{className:"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110",style:{backgroundColor:"rgba(16, 185, 129, 0.1)"},children:(0,r.jsx)(I.A,{className:"h-6 w-6 text-green-600"})}),(0,r.jsx)("p",{className:"font-semibold mb-1",style:{color:"dark"===i?"#f8fafc":"#111827"},children:"Record Debt"}),(0,r.jsx)("p",{className:"text-xs text-center",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:"Add customer debt record"})]}),(0,r.jsxs)("button",{onClick:()=>y("view-analytics"),className:"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2",style:{border:"dark"===i?"1px solid #475569":"1px solid #d1d5db",backgroundColor:"dark"===i?"#334155":"#f9fafb",background:"dark"===i?"linear-gradient(135deg, #334155 0%, #475569 100%)":"linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)"},title:"Navigate to API Graphing & Visuals for business analytics",children:[(0,r.jsx)("div",{className:"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110",style:{backgroundColor:"rgba(245, 158, 11, 0.1)"},children:(0,r.jsx)(j.A,{className:"h-6 w-6 text-yellow-600"})}),(0,r.jsx)("p",{className:"font-semibold mb-1",style:{color:"dark"===i?"#f8fafc":"#111827"},children:"View Analytics"}),(0,r.jsx)("p",{className:"text-xs text-center",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:"Business insights & reports"})]}),(0,r.jsxs)("button",{onClick:()=>y("manage-stock"),className:"group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2",style:{border:"dark"===i?"1px solid #475569":"1px solid #d1d5db",backgroundColor:"dark"===i?"#334155":"#f9fafb",background:"dark"===i?"linear-gradient(135deg, #334155 0%, #475569 100%)":"linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)"},title:"Navigate to Products section to manage inventory levels",children:[(0,r.jsx)("div",{className:"p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110",style:{backgroundColor:"rgba(139, 69, 19, 0.1)"},children:(0,r.jsx)(z.A,{className:"h-6 w-6 text-amber-700"})}),(0,r.jsx)("p",{className:"font-semibold mb-1",style:{color:"dark"===i?"#f8fafc":"#111827"},children:"Manage Stock"}),(0,r.jsx)("p",{className:"text-xs text-center",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:"Update inventory levels"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"rounded-2xl shadow-lg p-6 border transition-all duration-300",style:{backgroundColor:"dark"===i?"#1e293b":"#ffffff",border:"dark"===i?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===i?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(F.A,{className:"h-6 w-6 text-blue-500"}),(0,r.jsx)("h3",{className:"text-xl font-bold transition-colors duration-300",style:{color:"dark"===i?"#f8fafc":"#111827"},children:"Store Overview"})]}),(0,r.jsx)("div",{className:"space-y-4",children:[{label:"Products in List",value:g.totalProducts,icon:n.A,color:"#3b82f6"},{label:"Outstanding Debts",value:g.totalDebts,icon:d.A,color:"#10b981"},{label:"Total Amount Owed",value:b(g.totalDebtAmount),icon:S.A,color:"#f59e0b"},{label:"Items Need Restocking",value:g.lowStockItems,icon:D.A,color:g.lowStockItems>0?"#ef4444":"#10b981"}].map((e,a)=>{let t=e.icon;return(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl transition-all duration-300 hover:scale-[1.02] border",style:{backgroundColor:"dark"===i?"#334155":"#f9fafb",border:"dark"===i?"1px solid #475569":"1px solid #e5e7eb"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg",style:{backgroundColor:"".concat(e.color,"20")},children:(0,r.jsx)(t,{className:"h-5 w-5",style:{color:e.color}})}),(0,r.jsx)("span",{className:"font-medium transition-colors duration-300",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:e.label})]}),(0,r.jsx)("span",{className:"text-lg font-bold transition-colors duration-300",style:{color:"dark"===i?"#f8fafc":"#111827"},children:"string"==typeof e.value?e.value:e.value.toLocaleString()})]},a)})})]}),(0,r.jsxs)("div",{className:"rounded-2xl shadow-lg p-6 border transition-all duration-300",style:{backgroundColor:"dark"===i?"#1e293b":"#ffffff",border:"dark"===i?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===i?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)"},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(B.A,{className:"h-6 w-6 text-green-500"}),(0,r.jsx)("h3",{className:"text-xl font-bold transition-colors duration-300",style:{color:"dark"===i?"#f8fafc":"#111827"},children:"Recent Activities"})]}),(0,r.jsx)("button",{onClick:()=>y("view-history"),className:"text-sm px-3 py-1 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1",style:{backgroundColor:"dark"===i?"rgba(34, 197, 94, 0.1)":"rgba(34, 197, 94, 0.08)",color:"dark"===i?"#4ade80":"#16a34a"},title:"Navigate to History section for detailed activity logs",children:"View All"})]}),(0,r.jsx)("div",{className:"space-y-3",children:[{action:"New product added",item:"Coca Cola 1.5L",time:"2 minutes ago",type:"product",icon:n.A,priority:"normal",clickable:!0},{action:"Debt payment received",item:"Juan Dela Cruz - ₱500",time:"15 minutes ago",type:"payment",icon:S.A,priority:"high",clickable:!0},{action:"Low stock alert",item:"Rice 25kg - Only 3 left",time:"1 hour ago",type:"alert",icon:D.A,priority:"urgent",clickable:!0},{action:"New customer debt",item:"Maria Santos - ₱1,200",time:"2 hours ago",type:"debt",icon:d.A,priority:"normal",clickable:!0}].map((e,a)=>{let t=e.icon,s=e=>{switch(e){case"product":return"#3b82f6";case"payment":return"#10b981";case"alert":return"#ef4444";case"debt":return"#f59e0b";default:return"#6b7280"}},l=(e=>{switch(e){case"urgent":return{color:"#ef4444",pulse:!0};case"high":return{color:"#f59e0b",pulse:!1};case"normal":return{color:"#10b981",pulse:!1};default:return{color:"#6b7280",pulse:!1}}})(e.priority);return(0,r.jsxs)("button",{onClick:()=>e.clickable&&y("product"===e.type?"add-product":"debt"===e.type?"record-debt":"view-history"),className:"w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-300 hover:scale-[1.01] border text-left focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 ".concat(e.clickable?"cursor-pointer":"cursor-default"),style:{backgroundColor:"dark"===i?"#334155":"#f9fafb",border:"dark"===i?"1px solid #475569":"1px solid #e5e7eb"},disabled:!e.clickable,title:e.clickable?"Click to navigate to ".concat(e.type," section"):"",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"p-2 rounded-full flex-shrink-0 ".concat(l.pulse?"animate-pulse":""),style:{backgroundColor:"".concat(s(e.type),"20")},children:(0,r.jsx)(t,{className:"h-4 w-4",style:{color:s(e.type)}})}),(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 ".concat(l.pulse?"animate-ping":""),style:{backgroundColor:l.color,borderColor:"dark"===i?"#334155":"#f9fafb"}})]}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium truncate",style:{color:"dark"===i?"#f8fafc":"#111827"},children:e.action}),(0,r.jsx)("p",{className:"text-xs truncate",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:e.item})]}),(0,r.jsx)("span",{className:"text-xs flex-shrink-0",style:{color:"dark"===i?"#94a3b8":"#9ca3af"},children:e.time})]},a)})})]})]}),(0,r.jsxs)("div",{className:"rounded-2xl shadow-lg p-6 border transition-all duration-300",style:{backgroundColor:"dark"===i?"#1e293b":"#ffffff",border:"dark"===i?"1px solid #334155":"1px solid #e5e7eb",background:"dark"===i?"linear-gradient(135deg, #1e293b 0%, #334155 100%)":"linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)"},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)(U.A,{className:"h-6 w-6 text-purple-500"}),(0,r.jsx)("h3",{className:"text-xl font-bold transition-colors duration-300",style:{color:"dark"===i?"#f8fafc":"#111827"},children:"Performance Summary"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative w-20 h-20 mx-auto mb-3",children:[(0,r.jsxs)("svg",{className:"w-20 h-20 transform -rotate-90",viewBox:"0 0 36 36",children:[(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"dark"===i?"#334155":"#e5e7eb",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#10b981",strokeWidth:"2",strokeDasharray:"85, 100",className:"animate-pulse"})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-green-500",children:"85%"})})]}),(0,r.jsx)("p",{className:"text-sm font-medium",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:"Store Health"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative w-20 h-20 mx-auto mb-3",children:[(0,r.jsxs)("svg",{className:"w-20 h-20 transform -rotate-90",viewBox:"0 0 36 36",children:[(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"dark"===i?"#334155":"#e5e7eb",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#3b82f6",strokeWidth:"2",strokeDasharray:"72, 100",className:"animate-pulse"})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-blue-500",children:"72%"})})]}),(0,r.jsx)("p",{className:"text-sm font-medium",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:"Inventory Level"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative w-20 h-20 mx-auto mb-3",children:[(0,r.jsxs)("svg",{className:"w-20 h-20 transform -rotate-90",viewBox:"0 0 36 36",children:[(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"dark"===i?"#334155":"#e5e7eb",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#f59e0b",strokeWidth:"2",strokeDasharray:"58, 100",className:"animate-pulse"})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-lg font-bold text-yellow-500",children:"58%"})})]}),(0,r.jsx)("p",{className:"text-sm font-medium",style:{color:"dark"===i?"#cbd5e1":"#6b7280"},children:"Debt Recovery"})]})]})]})]})}var H=t(4792),q=t(8500),W=t(3109),Y=t(6932),V=t(1788),G=t(9881),K=t(8832),J=t(7712),Z=t(2657),Q=t(646);function X(e){let{stats:a}=e,{resolvedTheme:t}=(0,s.D)(),[i,o]=(0,l.useState)({salesData:[],debtData:[],categoryData:[],trendData:[],performanceMetrics:{revenue:{current:0,previous:0,change:0},customers:{current:0,previous:0,change:0},products:{current:0,previous:0,change:0},efficiency:{current:0,previous:0,change:0}}}),[c,x]=(0,l.useState)({dateRange:"month",chartType:"line",dataType:"all",showTrends:!0,showForecasting:!1}),[m,g]=(0,l.useState)(!1),[u,h]=(0,l.useState)(new Date),[p,b]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=()=>{b(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let y=(0,l.useCallback)(()=>{g(!0);let e=()=>[.8,.85,.9,1,1.1,1.2,1.3,1.25,1.15,1.05,.95,.9].map((e,a)=>Math.floor(25e3*e*(1+(Math.random()-.5)*.2))),t=()=>[.9,1,1.1,1.2,1.3,1.1,.8].map(e=>Math.floor(8e3*e*(1+(Math.random()-.5)*.15))),r=()=>[{name:"Beverages",value:35,color:"#22c55e"},{name:"Snacks",value:28,color:"#3b82f6"},{name:"Household",value:20,color:"#f59e0b"},{name:"Personal Care",value:12,color:"#ef4444"},{name:"Others",value:5,color:"#8b5cf6"}].map(e=>({...e,value:e.value+Math.floor((Math.random()-.5)*10)})),s=()=>["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((e,a)=>{let t=2e4+2e3*a+Math.floor(5e3*Math.random()),r=5e3+Math.floor(3e3*Math.random());return{month:e,sales:t,debt:r,profit:.3*t-.1*r}}),l=(e,a)=>{let t=e.reduce((e,a)=>e+a,0),r=t*(.85+.3*Math.random());return{revenue:{current:t,previous:r,change:(t-r)/r*100},customers:{current:a.totalDebts,previous:Math.floor(a.totalDebts*(.9+.2*Math.random())),change:Math.floor((Math.random()-.5)*20)},products:{current:a.totalProducts,previous:Math.floor(a.totalProducts*(.95+.1*Math.random())),change:Math.floor((Math.random()-.3)*15)},efficiency:{current:85+Math.floor(10*Math.random()),previous:80+Math.floor(10*Math.random()),change:Math.floor((Math.random()-.4)*10)}}};setTimeout(()=>{let i=e(),n=t(),d=r(),c=s(),x=l(i,a);o({salesData:i,debtData:n,categoryData:d,trendData:c,performanceMetrics:x}),h(new Date),g(!1)},800)},[a]);(0,l.useEffect)(()=>{y()},[y,c.dateRange]);let f=()=>({backgroundColor:"dark"===t?"#1e293b":"#ffffff",textStyle:{color:"dark"===t?"#f1f5f9":"#1f2937",fontFamily:"Inter, system-ui, sans-serif"},grid:{borderColor:"dark"===t?"#334155":"#e5e7eb"}}),j=(0,l.useMemo)(()=>({...f(),title:{text:"Monthly Sales Revenue",textStyle:{fontSize:18,fontWeight:"bold",color:"dark"===t?"#f1f5f9":"#1f2937"},left:"center",top:10},tooltip:{trigger:"axis",backgroundColor:"dark"===t?"#374151":"#ffffff",borderColor:"dark"===t?"#4b5563":"#e5e7eb",textStyle:{color:"dark"===t?"#f9fafb":"#111827"},formatter:e=>{let a=e[0];return'\n          <div style="padding: 8px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">'.concat(a.name,'</div>\n            <div style="display: flex; align-items: center;">\n              <div style="width: 10px; height: 10px; background: ').concat(a.color,'; border-radius: 50%; margin-right: 8px;"></div>\n              Revenue: ₱').concat(a.value.toLocaleString(),'\n            </div>\n            <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">\n              ').concat(a.value>(i.salesData[a.dataIndex-1]||0)?"↗️ Increased":"↘️ Decreased"," from previous month\n            </div>\n          </div>\n        ")}},legend:{show:!0,top:40,textStyle:{color:"dark"===t?"#cbd5e1":"#4b5563"}},xAxis:{type:"category",data:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],axisLine:{lineStyle:{color:"dark"===t?"#475569":"#d1d5db"}},axisLabel:{color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12}},yAxis:{type:"value",axisLabel:{formatter:"₱{value}",color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12},axisLine:{lineStyle:{color:"dark"===t?"#475569":"#d1d5db"}},splitLine:{lineStyle:{color:"dark"===t?"#334155":"#f3f4f6",type:"dashed"}}},series:[{name:"Revenue",data:i.salesData,type:c.chartType,smooth:!0,lineStyle:{color:"#22c55e",width:3},itemStyle:{color:"#22c55e",borderRadius:"bar"===c.chartType?[4,4,0,0]:0},areaStyle:"area"===c.chartType?{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(34, 197, 94, 0.4)"},{offset:1,color:"rgba(34, 197, 94, 0.05)"}]}}:void 0,emphasis:{focus:"series",itemStyle:{shadowBlur:10,shadowColor:"rgba(34, 197, 94, 0.5)"}},markPoint:{data:[{type:"max",name:"Max"},{type:"min",name:"Min"}],itemStyle:{color:"#facc15"}},markLine:c.showTrends?{data:[{type:"average",name:"Average"}],lineStyle:{color:"#f59e0b",type:"dashed"}}:void 0}],grid:{left:"3%",right:"4%",bottom:"10%",top:"15%",containLabel:!0},dataZoom:[{type:"inside",start:0,end:100},{start:0,end:100,handleIcon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",handleSize:"80%",handleStyle:{color:"#22c55e",shadowBlur:3,shadowColor:"rgba(0, 0, 0, 0.6)",shadowOffsetX:2,shadowOffsetY:2}}],toolbox:{feature:{dataZoom:{yAxisIndex:"none"},restore:{},saveAsImage:{pixelRatio:2}},iconStyle:{borderColor:"dark"===t?"#9ca3af":"#6b7280"}}}),[i.salesData,c.chartType,t]),v=(0,l.useMemo)(()=>({...f(),title:{text:"Weekly Customer Debt Trends",textStyle:{fontSize:18,fontWeight:"bold",color:"dark"===t?"#f1f5f9":"#1f2937"},left:"center",top:10},tooltip:{trigger:"axis",backgroundColor:"dark"===t?"#374151":"#ffffff",borderColor:"dark"===t?"#4b5563":"#e5e7eb",textStyle:{color:"dark"===t?"#f9fafb":"#111827"},formatter:e=>{let a=e[0],t=(a.value/i.debtData.reduce((e,a)=>e+a,0)*100).toFixed(1);return'\n          <div style="padding: 8px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">'.concat(a.name,'</div>\n            <div style="display: flex; align-items: center;">\n              <div style="width: 10px; height: 10px; background: ').concat(a.color,'; border-radius: 2px; margin-right: 8px;"></div>\n              Total Debt: ₱').concat(a.value.toLocaleString(),'\n            </div>\n            <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">\n              ').concat(t,"% of weekly total\n            </div>\n          </div>\n        ")}},legend:{show:!0,top:40,textStyle:{color:"dark"===t?"#cbd5e1":"#4b5563"}},xAxis:{type:"category",data:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],axisLine:{lineStyle:{color:"dark"===t?"#475569":"#d1d5db"}},axisLabel:{color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12,rotate:45}},yAxis:{type:"value",axisLabel:{formatter:"₱{value}",color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12},axisLine:{lineStyle:{color:"dark"===t?"#475569":"#d1d5db"}},splitLine:{lineStyle:{color:"dark"===t?"#334155":"#f3f4f6",type:"dashed"}}},series:[{name:"Customer Debt",data:i.debtData,type:"bar",itemStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"#facc15"},{offset:1,color:"#eab308"}]},borderRadius:[4,4,0,0]},emphasis:{focus:"series",itemStyle:{color:"#f59e0b",shadowBlur:10,shadowColor:"rgba(245, 158, 11, 0.5)"}},markPoint:{data:[{type:"max",name:"Peak Day"},{type:"min",name:"Low Day"}],itemStyle:{color:"#ef4444"}}}],grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},toolbox:{feature:{dataZoom:{yAxisIndex:"none"},restore:{},saveAsImage:{pixelRatio:2}},iconStyle:{borderColor:"dark"===t?"#9ca3af":"#6b7280"}}}),[i.debtData,t]),k=(0,l.useMemo)(()=>({...f(),title:{text:"Product Categories Distribution",textStyle:{fontSize:18,fontWeight:"bold",color:"dark"===t?"#f1f5f9":"#1f2937"},left:"center",top:10},tooltip:{trigger:"item",backgroundColor:"dark"===t?"#374151":"#ffffff",borderColor:"dark"===t?"#4b5563":"#e5e7eb",textStyle:{color:"dark"===t?"#f9fafb":"#111827"},formatter:e=>'\n          <div style="padding: 8px;">\n            <div style="font-weight: bold; margin-bottom: 4px;">'.concat(e.name,'</div>\n            <div style="display: flex; align-items: center;">\n              <div style="width: 10px; height: 10px; background: ').concat(e.color,'; border-radius: 50%; margin-right: 8px;"></div>\n              Value: ').concat(e.value,'%\n            </div>\n            <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">\n              ').concat(e.percent,"% of total sales\n            </div>\n          </div>\n        ")},legend:{orient:"horizontal",bottom:10,textStyle:{color:"dark"===t?"#cbd5e1":"#4b5563"}},series:[{name:"Categories",type:"pie",radius:["45%","75%"],center:["50%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:8,borderColor:"dark"===t?"#1e293b":"#ffffff",borderWidth:2},label:{show:!0,position:"outside",formatter:"{b}: {c}%",color:"dark"===t?"#cbd5e1":"#4b5563",fontSize:12},emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"},label:{show:!0,fontSize:14,fontWeight:"bold"}},labelLine:{show:!0,lineStyle:{color:"dark"===t?"#475569":"#d1d5db"}},data:i.categoryData}]}),[i.categoryData,t]),w=(0,l.useMemo)(()=>{let e=Array.from({length:24},(e,a)=>"".concat(a,":00")),a=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],r=[];for(let e=0;e<7;e++)for(let a=0;a<24;a++){let t=Math.floor(100*Math.random())+10;r.push([a,e,t])}return{...f(),title:{text:"Sales Activity Heatmap",textStyle:{fontSize:18,fontWeight:"bold",color:"dark"===t?"#f1f5f9":"#1f2937"},left:"center",top:10},tooltip:{position:"top",backgroundColor:"dark"===t?"#374151":"#ffffff",borderColor:"dark"===t?"#4b5563":"#e5e7eb",textStyle:{color:"dark"===t?"#f9fafb":"#111827"},formatter:t=>'\n            <div style="padding: 8px;">\n              <div style="font-weight: bold; margin-bottom: 4px;">'.concat(a[t.data[1]]," ").concat(e[t.data[0]],'</div>\n              <div style="display: flex; align-items: center;">\n                <div style="width: 10px; height: 10px; background: ').concat(t.color,'; border-radius: 2px; margin-right: 8px;"></div>\n                Sales Activity: ').concat(t.data[2],"%\n              </div>\n            </div>\n          ")},grid:{height:"60%",top:"15%"},xAxis:{type:"category",data:e,splitArea:{show:!0},axisLabel:{color:"dark"===t?"#94a3b8":"#6b7280",fontSize:10}},yAxis:{type:"category",data:a,splitArea:{show:!0},axisLabel:{color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12}},visualMap:{min:0,max:100,calculable:!0,orient:"horizontal",left:"center",bottom:"5%",inRange:{color:["#313695","#4575b4","#74add1","#abd9e9","#e0f3f8","#ffffbf","#fee090","#fdae61","#f46d43","#d73027","#a50026"]},textStyle:{color:"dark"===t?"#cbd5e1":"#4b5563"}},series:[{name:"Sales Activity",type:"heatmap",data:r,label:{show:!1},emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}},[t]),A=(0,l.useMemo)(()=>({...f(),title:{text:"Business Performance",textStyle:{fontSize:18,fontWeight:"bold",color:"dark"===t?"#f1f5f9":"#1f2937"},left:"center",top:10},series:[{name:"Performance",type:"gauge",center:["50%","60%"],startAngle:200,endAngle:-40,min:0,max:100,splitNumber:10,itemStyle:{color:"#22c55e"},progress:{show:!0,width:30},pointer:{show:!1},axisLine:{lineStyle:{width:30,color:[[.3,"#ef4444"],[.7,"#f59e0b"],[1,"#22c55e"]]}},axisTick:{distance:-45,splitNumber:5,lineStyle:{width:2,color:"dark"===t?"#475569":"#d1d5db"}},splitLine:{distance:-52,length:14,lineStyle:{width:3,color:"dark"===t?"#475569":"#d1d5db"}},axisLabel:{distance:-20,color:"dark"===t?"#94a3b8":"#6b7280",fontSize:12},anchor:{show:!1},title:{show:!1},detail:{valueAnimation:!0,width:"60%",lineHeight:40,borderRadius:8,offsetCenter:[0,"-15%"],fontSize:24,fontWeight:"bold",formatter:"{value}%",color:"dark"===t?"#f1f5f9":"#1f2937"},data:[{value:i.performanceMetrics.efficiency.current,name:"Efficiency"}]}]}),[i.performanceMetrics.efficiency.current,t]),C=(0,l.useMemo)(()=>{let e=i.salesData.reduce((e,a)=>e+a,0),a=e/i.salesData.length,t=i.debtData.reduce((e,a)=>e+a,0)/i.debtData.length;return[{title:"Total Revenue",value:"₱"+e.toLocaleString(),icon:S.A,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-50 dark:bg-green-900/20",change:"+12.5%",changeColor:"text-green-600 dark:text-green-400",trend:"up",subtitle:"Avg: ₱".concat(a.toLocaleString(),"/month")},{title:"Active Customers",value:i.performanceMetrics.customers.current.toString(),icon:d.A,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-50 dark:bg-blue-900/20",change:"".concat(i.performanceMetrics.customers.change>0?"+":"").concat(i.performanceMetrics.customers.change.toFixed(1),"%"),changeColor:i.performanceMetrics.customers.change>0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400",trend:i.performanceMetrics.customers.change>0?"up":"down",subtitle:"Customer base growth"},{title:"Products Listed",value:i.performanceMetrics.products.current.toString(),icon:n.A,color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-50 dark:bg-purple-900/20",change:"".concat(i.performanceMetrics.products.change>0?"+":"").concat(i.performanceMetrics.products.change.toFixed(1),"%"),changeColor:i.performanceMetrics.products.change>0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400",trend:i.performanceMetrics.products.change>0?"up":"down",subtitle:"Inventory expansion"},{title:"Business Efficiency",value:"".concat(i.performanceMetrics.efficiency.current,"%"),icon:F.A,color:"text-orange-600 dark:text-orange-400",bgColor:"bg-orange-50 dark:bg-orange-900/20",change:"".concat(i.performanceMetrics.efficiency.change>0?"+":"").concat(i.performanceMetrics.efficiency.change.toFixed(1),"%"),changeColor:i.performanceMetrics.efficiency.change>0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400",trend:i.performanceMetrics.efficiency.change>0?"up":"down",subtitle:"Operational performance"},{title:"Weekly Debt Avg",value:"₱"+t.toLocaleString(),icon:q.A,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-50 dark:bg-yellow-900/20",change:"-3.2%",changeColor:"text-green-600 dark:text-green-400",trend:"down",subtitle:"Daily average debt"},{title:"Growth Rate",value:"".concat((e/(.85*e)*100-100).toFixed(1),"%"),icon:W.A,color:"text-emerald-600 dark:text-emerald-400",bgColor:"bg-emerald-50 dark:bg-emerald-900/20",change:"+15.8%",changeColor:"text-green-600 dark:text-green-400",trend:"up",subtitle:"Monthly growth rate"}]},[i,a.totalDebtAmount]);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(()=>(0,r.jsx)("div",{className:"card p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(Y.A,{className:"h-4 w-4 text-gray-500 dark:text-gray-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Filters:"})]}),(0,r.jsxs)("select",{value:c.dateRange,onChange:e=>x(a=>({...a,dateRange:e.target.value})),className:"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"week",children:"This Week"}),(0,r.jsx)("option",{value:"month",children:"This Month"}),(0,r.jsx)("option",{value:"quarter",children:"This Quarter"}),(0,r.jsx)("option",{value:"year",children:"This Year"})]}),(0,r.jsxs)("select",{value:c.chartType,onChange:e=>x(a=>({...a,chartType:e.target.value})),className:"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-green-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"line",children:"Line Chart"}),(0,r.jsx)("option",{value:"bar",children:"Bar Chart"}),(0,r.jsx)("option",{value:"area",children:"Area Chart"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:y,disabled:m,className:"flex items-center space-x-2 px-3 py-1 text-sm bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200",children:[(0,r.jsx)(T.A,{className:"h-4 w-4 ".concat(m?"animate-spin":"")}),(0,r.jsx)("span",{children:m?"Updating...":"Refresh"})]}),(0,r.jsxs)("button",{className:"flex items-center space-x-2 px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200",children:[(0,r.jsx)(V.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Export"})]})]})]})}),{}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3 sm:gap-4",children:C.map((e,a)=>(0,r.jsx)("div",{className:"card p-4 hover:shadow-lg transition-all duration-300 group",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg ".concat(e.bgColor," group-hover:scale-110 transition-transform duration-200"),children:(0,r.jsx)(e.icon,{className:"h-5 w-5 ".concat(e.color)})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:["up"===e.trend&&(0,r.jsx)(G.A,{className:"h-3 w-3 text-green-500"}),"down"===e.trend&&(0,r.jsx)(K.A,{className:"h-3 w-3 text-red-500"}),"neutral"===e.trend&&(0,r.jsx)(J.A,{className:"h-3 w-3 text-gray-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide",children:e.title}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900 dark:text-white mt-1",children:e.value}),e.subtitle&&(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:e.subtitle}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("span",{className:"text-xs font-medium ".concat(e.changeColor),children:e.change}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"vs last period"})]})]})]})},a))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6",children:[(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 dark:bg-green-900/20 rounded-lg",children:(0,r.jsx)(W.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Sales Revenue"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Monthly performance trends"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Live"})]})})]}),m?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Loading chart data..."})]})}):(0,r.jsx)(H.A,{option:j,style:{height:p?"300px":"400px"}})]}),(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg",children:(0,r.jsx)(d.A,{className:"h-5 w-5 text-yellow-600 dark:text-yellow-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Customer Debt"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Weekly debt patterns"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Active"})]})})]}),m?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Loading chart data..."})]})}):(0,r.jsx)(H.A,{option:v,style:{height:p?"300px":"400px"}})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6",children:[(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg",children:(0,r.jsx)(n.A,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Product Categories"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Sales distribution by category"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200",children:(0,r.jsx)(Z.A,{className:"h-4 w-4 text-gray-500 dark:text-gray-400"})}),(0,r.jsx)("button",{className:"p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200",children:(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-500 dark:text-gray-400"})})]})]}),m?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Loading chart data..."})]})}):(0,r.jsx)(H.A,{option:k,style:{height:p?"300px":"400px"}})]}),(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-emerald-100 dark:bg-emerald-900/20 rounded-lg",children:(0,r.jsx)(F.A,{className:"h-5 w-5 text-emerald-600 dark:text-emerald-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Performance Gauge"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Overall business efficiency"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-emerald-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Real-time"})]})})]}),m?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Loading gauge data..."})]})}):(0,r.jsx)(H.A,{option:A,style:{height:p?"300px":"400px"}})]})]}),(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-shadow duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg",children:(0,r.jsx)(P.A,{className:"h-5 w-5 text-purple-600 dark:text-purple-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Sales Activity Heatmap"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Hourly sales patterns throughout the week"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Pattern Analysis"})]})})]}),m?(0,r.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Loading heatmap data..."})]})}):(0,r.jsx)(H.A,{option:w,style:{height:p?"400px":"500px"}})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Predictive Insights"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400 font-medium",children:"AI Powered"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(W.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-blue-800 dark:text-blue-300",children:"Revenue Forecast"})]}),(0,r.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-400",children:"Expected 15% growth next month based on current trends"}),(0,r.jsx)("div",{className:"mt-2 w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"75%"}})})]}),(0,r.jsxs)("div",{className:"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-300",children:"Customer Growth"})]}),(0,r.jsx)("p",{className:"text-xs text-green-700 dark:text-green-400",children:"New customer acquisition rate increasing by 8%"}),(0,r.jsx)("div",{className:"mt-2 w-full bg-green-200 dark:bg-green-800 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"68%"}})})]}),(0,r.jsxs)("div",{className:"p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-300",children:"Inventory Alert"})]}),(0,r.jsx)("p",{className:"text-xs text-yellow-700 dark:text-yellow-400",children:"3 products predicted to run low stock within 5 days"}),(0,r.jsx)("div",{className:"mt-2 w-full bg-yellow-200 dark:bg-yellow-800 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-yellow-600 h-2 rounded-full",style:{width:"30%"}})})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"System Status"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-sm text-green-600 dark:text-green-400 font-medium",children:"Online"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(P.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Data Streaming"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(Q.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)("span",{className:"text-sm text-green-600 dark:text-green-400",children:"Active"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(R.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"API Response"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400",children:"~250ms"})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(M.A,{className:"h-4 w-4 text-yellow-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Last Update"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:u.toLocaleTimeString()})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(F.A,{className:"h-4 w-4 text-purple-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Data Quality"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("span",{className:"text-sm text-purple-600 dark:text-purple-400",children:"98.5%"})})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Performance"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(F.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400 font-medium",children:"Optimized"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Chart Rendering"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"95%"}})}),(0,r.jsx)("span",{className:"text-sm text-green-600 dark:text-green-400",children:"95%"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Data Processing"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:"88%"}})}),(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400",children:"88%"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Memory Usage"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-yellow-500 h-2 rounded-full",style:{width:"72%"}})}),(0,r.jsx)("span",{className:"text-sm text-yellow-600 dark:text-yellow-400",children:"72%"})]})]})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"p-2 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg",children:(0,r.jsx)(W.A,{className:"h-5 w-5 text-indigo-600 dark:text-indigo-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Business Intelligence Summary"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Key insights and recommendations"})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-indigo-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Auto-generated"})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(Q.A,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-300",children:"Strong Performance"})]}),(0,r.jsx)("p",{className:"text-xs text-green-700 dark:text-green-400",children:"Revenue growth is exceeding targets by 12%. Continue current marketing strategies."})]}),(0,r.jsxs)("div",{className:"p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-blue-800 dark:text-blue-300",children:"Customer Retention"})]}),(0,r.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-400",children:"Customer loyalty programs showing positive impact. 85% retention rate achieved."})]}),(0,r.jsxs)("div",{className:"p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-300",children:"Inventory Optimization"})]}),(0,r.jsx)("p",{className:"text-xs text-yellow-700 dark:text-yellow-400",children:"Consider increasing stock for high-demand items. Seasonal patterns identified."})]}),(0,r.jsxs)("div",{className:"p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(F.A,{className:"h-4 w-4 text-purple-600 dark:text-purple-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-purple-800 dark:text-purple-300",children:"Efficiency Gains"})]}),(0,r.jsx)("p",{className:"text-xs text-purple-700 dark:text-purple-400",children:"Operational efficiency improved by 15%. Focus on peak hour optimization."})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,r.jsx)(P.A,{className:"h-4 w-4 text-gray-600 dark:text-gray-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-800 dark:text-gray-300",children:"Next Actions"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,r.jsx)("span",{className:"text-xs text-gray-700 dark:text-gray-400",children:"Expand successful product lines"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,r.jsx)("span",{className:"text-xs text-gray-700 dark:text-gray-400",children:"Implement customer feedback system"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,r.jsx)("span",{className:"text-xs text-gray-700 dark:text-gray-400",children:"Optimize inventory turnover"})]})]})]})]})]})}var $=t(3717),ee=t(2525),ea=t(4416),et=t(9869),er=t(5647),es=t(8663),el=t(4028),ei=t(9509);let en=es.Ik({NODE_ENV:es.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:es.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:es.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:es.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:es.Yj().optional(),CLOUDINARY_API_KEY:es.Yj().optional(),CLOUDINARY_API_SECRET:es.Yj().optional(),GEMINI_API_KEY:es.Yj().optional(),NEXTAUTH_SECRET:es.Yj().optional(),NEXTAUTH_URL:es.Yj().optional(),DEBUG:es.Yj().transform(e=>"true"===e).default("false")}),ed=function(){try{return en.parse(ei.env)}catch(e){if(e instanceof el.G){let a=e.errors.map(e=>"".concat(e.path.join("."),": ").concat(e.message));throw Error("❌ Invalid environment variables:\n".concat(a.join("\n"),"\n\n")+"Please check your .env.local file and ensure all required variables are set.\nSee .env.example for reference.")}throw e}}(),eo={isDevelopment:"development"===ed.NODE_ENV,isProduction:"production"===ed.NODE_ENV,isTest:"test"===ed.NODE_ENV,database:{url:ed.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:ed.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:ed.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:ed.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:ed.CLOUDINARY_API_KEY,apiSecret:ed.CLOUDINARY_API_SECRET},ai:{geminiApiKey:ed.GEMINI_API_KEY},auth:{secret:ed.NEXTAUTH_SECRET,url:ed.NEXTAUTH_URL},debug:ed.DEBUG},{NODE_ENV:ec,NEXT_PUBLIC_SUPABASE_URL:ex,NEXT_PUBLIC_SUPABASE_ANON_KEY:em,SUPABASE_SERVICE_ROLE_KEY:eg,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:eu,CLOUDINARY_API_KEY:eh,CLOUDINARY_API_SECRET:ep,NEXTAUTH_SECRET:eb,NEXTAUTH_URL:ey,DEBUG:ef,GEMINI_API_KEY:ej}=ed;(0,er.UU)(eo.database.url,eo.database.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"revantad-store@1.0.0"}}});let ev=["Snacks","Canned Goods","Beverages","Personal Care","Household Items","Condiments","Rice & Grains","Instant Foods","Dairy Products","Others"];function ek(e){let{isOpen:a,onClose:t,product:s}=e,[i,d]=(0,l.useState)({name:"",net_weight:"",price:"",stock_quantity:"",category:"",image_url:""}),[o,c]=(0,l.useState)(null),[x,m]=(0,l.useState)(""),[g,u]=(0,l.useState)(!1),[h,p]=(0,l.useState)(!1);(0,l.useEffect)(()=>{s?(d({name:s.name,net_weight:s.net_weight,price:s.price.toString(),stock_quantity:s.stock_quantity.toString(),category:s.category,image_url:s.image_url||""}),m(s.image_url||"")):(d({name:"",net_weight:"",price:"",stock_quantity:"",category:"",image_url:""}),m("")),c(null)},[s,a]);let b=async()=>{if(!o)return i.image_url;p(!0);try{let e=new FormData;e.append("file",o);let a=await fetch("/api/upload",{method:"POST",body:e});return(await a.json()).url}catch(e){return console.error("Error uploading image:",e),i.image_url}finally{p(!1)}},y=async e=>{e.preventDefault(),u(!0);try{let e=await b(),a={...i,image_url:e,price:parseFloat(i.price),stock_quantity:parseInt(i.stock_quantity)},r=s?"/api/products/".concat(s.id):"/api/products",l=s?"PUT":"POST";(await fetch(r,{method:l,headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).ok?t():console.error("Error saving product")}catch(e){console.error("Error saving product:",e)}finally{u(!1)}};return a?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:s?"Edit Product in List":"Add Product to List"}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(ea.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Product Image"}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-2",children:x?(0,r.jsx)("img",{src:x,alt:"Preview",className:"w-full h-full object-cover rounded-lg"}):(0,r.jsx)(n.A,{className:"h-12 w-12 text-gray-400"})}),(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];if(t){c(t);let e=new FileReader;e.onloadend=()=>{m(e.result)},e.readAsDataURL(t)}},className:"hidden",id:"image-upload"}),(0,r.jsxs)("label",{htmlFor:"image-upload",className:"flex items-center px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50",children:[(0,r.jsx)(et.A,{className:"h-4 w-4 mr-2"}),"Choose Image"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:i.name,onChange:e=>d({...i,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Net Weight *"}),(0,r.jsx)("input",{type:"text",required:!0,placeholder:"e.g., 100g, 1L, 250ml",value:i.net_weight,onChange:e=>d({...i,net_weight:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price (₱) *"}),(0,r.jsx)("input",{type:"number",step:"0.01",min:"0",required:!0,value:i.price,onChange:e=>d({...i,price:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Stock Quantity *"}),(0,r.jsx)("input",{type:"number",min:"0",required:!0,value:i.stock_quantity,onChange:e=>d({...i,stock_quantity:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category *"}),(0,r.jsxs)("select",{required:!0,value:i.category,onChange:e=>d({...i,category:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",children:"Select Category"}),ev.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:g||h,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:g||h?"Saving...":s?"Update in List":"Add to List"})]})]})]})}):null}function eN(e){let{onStatsUpdate:a}=e,{resolvedTheme:t}=(0,s.D)(),[i,d]=(0,l.useState)([]),[o,x]=(0,l.useState)(!0),[m,g]=(0,l.useState)(""),[u,h]=(0,l.useState)(""),[p,b]=(0,l.useState)(!1),[y,f]=(0,l.useState)(null);(0,l.useEffect)(()=>{j()},[]);let j=async()=>{try{let e=await fetch("/api/products"),a=await e.json();d(a.products||[])}catch(e){console.error("Error fetching products:",e)}finally{x(!1)}},v=async e=>{if(confirm("Are you sure you want to delete this product?"))try{(await fetch("/api/products/".concat(e),{method:"DELETE"})).ok&&(d(i.filter(a=>a.id!==e)),a())}catch(e){console.error("Error deleting product:",e)}},k=e=>{f(e),b(!0)},N=i.filter(e=>{let a=e.name.toLowerCase().includes(m.toLowerCase()),t=""===u||e.category===u;return a&&t});return o?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300",style:{color:"dark"===t?"#9ca3af":"#6b7280"}}),(0,r.jsx)("input",{type:"text",placeholder:"Search products...",value:m,onChange:e=>g(e.target.value),className:"pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===t?"#374151":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===t?"#f9fafb":"#111827"}})]}),(0,r.jsxs)("select",{value:u,onChange:e=>h(e.target.value),className:"px-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===t?"#374151":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===t?"#f9fafb":"#111827"},children:[(0,r.jsx)("option",{value:"",children:"All Categories"}),ev.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)("button",{onClick:()=>b(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg",children:[(0,r.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"Add to Product List"]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:N.map(e=>(0,r.jsxs)("div",{className:"rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02]",style:{backgroundColor:"dark"===t?"#1e293b":"#ffffff",border:"dark"===t?"1px solid #334155":"1px solid #e5e7eb"},children:[(0,r.jsx)("div",{className:"aspect-square flex items-center justify-center transition-colors duration-300",style:{backgroundColor:"dark"===t?"#374151":"#f3f4f6"},children:e.image_url?(0,r.jsx)("img",{src:e.image_url,alt:e.name,className:"w-full h-full object-cover"}):(0,r.jsx)(n.A,{className:"h-16 w-16 transition-colors duration-300",style:{color:"dark"===t?"#9ca3af":"#6b7280"}})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-semibold mb-1 transition-colors duration-300",style:{color:"dark"===t?"#f8fafc":"#111827"},children:e.name}),(0,r.jsx)("p",{className:"text-sm mb-2 transition-colors duration-300",style:{color:"dark"===t?"#cbd5e1":"#6b7280"},children:e.category}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₱",e.price]}),(0,r.jsx)("span",{className:"text-sm transition-colors duration-300",style:{color:"dark"===t?"#9ca3af":"#6b7280"},children:e.net_weight})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("span",{className:"text-sm ".concat(e.stock_quantity<10?"text-red-600":""),style:{color:e.stock_quantity>=10?"dark"===t?"#cbd5e1":"#6b7280":"#dc2626"},children:["Stock: ",e.stock_quantity]}),e.stock_quantity<10&&(0,r.jsx)("span",{className:"text-xs bg-red-100 text-red-800 px-2 py-1 rounded",children:"Low Stock"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>k(e),className:"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors",children:[(0,r.jsx)($.A,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,r.jsxs)("button",{onClick:()=>v(e.id),className:"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors",children:[(0,r.jsx)(ee.A,{className:"h-4 w-4 mr-1"}),"Delete"]})]})]})]},e.id))}),0===N.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(n.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products in list"}),(0,r.jsx)("p",{className:"text-gray-600",children:m||u?"Try adjusting your search or filter criteria":"Get started by adding your first product to the list"})]}),(0,r.jsx)(ek,{isOpen:p,onClose:()=>{b(!1),f(null),j(),a()},product:y})]})}function ew(e){let{isOpen:a,onClose:t,debt:s}=e,[i,n]=(0,l.useState)({customer_name:"",customer_family_name:"",product_name:"",product_price:"",quantity:"",debt_date:""}),[d,o]=(0,l.useState)(!1);(0,l.useEffect)(()=>{s?n({customer_name:s.customer_name,customer_family_name:s.customer_family_name,product_name:s.product_name,product_price:s.product_price.toString(),quantity:s.quantity.toString(),debt_date:s.debt_date}):n({customer_name:"",customer_family_name:"",product_name:"",product_price:"",quantity:"",debt_date:new Date().toISOString().split("T")[0]||""})},[s,a]);let c=async e=>{e.preventDefault(),o(!0);try{let e={...i,product_price:parseFloat(i.product_price),quantity:parseInt(i.quantity)},a=s?"/api/debts/".concat(s.id):"/api/debts",r=s?"PUT":"POST";(await fetch(a,{method:r,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?t():console.error("Error saving debt record")}catch(e){console.error("Error saving debt record:",e)}finally{o(!1)}};if(!a)return null;let x=i.product_price&&i.quantity?(parseFloat(i.product_price)*parseInt(i.quantity)).toFixed(2):"0.00";return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:s?"Edit Debt Record":"Add New Debt Record"}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(ea.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("form",{onSubmit:c,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Customer First Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:i.customer_name,onChange:e=>n({...i,customer_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Juan"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Customer Family Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:i.customer_family_name,onChange:e=>n({...i,customer_family_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Dela Cruz"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name *"}),(0,r.jsx)("input",{type:"text",required:!0,value:i.product_name,onChange:e=>n({...i,product_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Lucky Me Pancit Canton"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Price (₱) *"}),(0,r.jsx)("input",{type:"number",step:"0.01",min:"0",required:!0,value:i.product_price,onChange:e=>n({...i,product_price:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"0.00"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Quantity *"}),(0,r.jsx)("input",{type:"number",min:"1",required:!0,value:i.quantity,onChange:e=>n({...i,quantity:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"1"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Debt Date *"}),(0,r.jsx)("input",{type:"date",required:!0,value:i.debt_date,onChange:e=>n({...i,debt_date:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsx)("div",{className:"bg-gray-50 p-3 rounded-md",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Total Amount:"}),(0,r.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₱",x]})]})}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:d,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:d?"Saving...":s?"Update":"Add Record"})]})]})]})})}var eA=t(3319);function eC(e){let{onStatsUpdate:a}=e,{resolvedTheme:t}=(0,s.D)(),[i,n]=(0,l.useState)([]),[o,x]=(0,l.useState)(!0),[m,g]=(0,l.useState)(""),[u,h]=(0,l.useState)(!1),[p,b]=(0,l.useState)(null);(0,l.useEffect)(()=>{y()},[]);let y=async()=>{try{let e=await fetch("/api/debts"),a=await e.json();n(a.debts||[])}catch(e){console.error("Error fetching debts:",e)}finally{x(!1)}},f=async e=>{if(confirm("Are you sure you want to delete this debt record?"))try{(await fetch("/api/debts/".concat(e),{method:"DELETE"})).ok&&(n(i.filter(a=>a.id!==e)),a())}catch(e){console.error("Error deleting debt:",e)}},j=e=>{b(e),h(!0)},v=i.filter(e=>{let a="".concat(e.customer_name," ").concat(e.customer_family_name).toLowerCase(),t=e.product_name.toLowerCase(),r=m.toLowerCase();return a.includes(r)||t.includes(r)}),N=v.reduce((e,a)=>{let t="".concat(a.customer_name," ").concat(a.customer_family_name);return e[t]||(e[t]=[]),e[t].push(a),e},{});return o?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300",style:{color:"dark"===t?"#9ca3af":"#6b7280"}}),(0,r.jsx)("input",{type:"text",placeholder:"Search by customer or product...",value:m,onChange:e=>g(e.target.value),className:"pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===t?"#374151":"#ffffff",border:"dark"===t?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===t?"#f9fafb":"#111827"}})]}),(0,r.jsxs)("button",{onClick:()=>h(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg",children:[(0,r.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"Add Debt Record"]})]}),(0,r.jsx)("div",{className:"space-y-6",children:Object.entries(N).map(e=>{let[a,t]=e,s=t.reduce((e,a)=>e+a.total_amount,0);return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-gray-400 mr-2"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:a})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[t.length," item(s)"]}),(0,r.jsxs)("p",{className:"text-lg font-bold text-red-600",children:["₱",s.toFixed(2)]})]})]})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:t.map(e=>(0,r.jsx)("div",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.product_name}),(0,r.jsxs)("div",{className:"mt-1 text-sm text-gray-600 space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("span",{children:["Quantity: ",e.quantity]}),(0,r.jsx)("span",{className:"mx-2",children:"•"}),(0,r.jsxs)("span",{children:["Unit Price: ₱",e.product_price.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(k.A,{className:"h-4 w-4 mr-1"}),(0,r.jsxs)("span",{children:["Date: ",(0,eA.GP)(new Date(e.debt_date),"MMM dd, yyyy")]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,r.jsx)("div",{className:"text-right",children:(0,r.jsxs)("p",{className:"font-semibold text-gray-900",children:["₱",e.total_amount.toFixed(2)]})}),(0,r.jsx)("button",{onClick:()=>j(e),className:"p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors",children:(0,r.jsx)($.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>f(e.id),className:"p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors",children:(0,r.jsx)(ee.A,{className:"h-4 w-4"})})]})]})},e.id))})]},a)})}),0===v.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(d.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No debt records found"}),(0,r.jsx)("p",{className:"text-gray-600",children:m?"Try adjusting your search criteria":"Get started by adding your first debt record"})]}),(0,r.jsx)(ew,{isOpen:u,onClose:()=>{h(!1),b(null),y(),a()},debt:p})]})}var eS=t(4653),eD=t(4395),eP=t(3332),eM=t(1976),eT=t(8564),eL=t(6736),eE=t(5968),eR=t(5213),e_=t(5488),eI=t(6516),ez=t(4020),eF=t(2178),eB=t(5690);function eU(){var e;let[a,t]=(0,l.useState)([{id:"1",url:"/api/placeholder/400/300",title:"Pag-bukas sa Tindahan",description:"Grand opening sa aming Revantad Store kauban ang buong pamilya",date:"2024-01-15",likes:12,isLiked:!0,category:"store",tags:["opening","family","milestone"],views:45,isPrivate:!1,uploadedBy:"Admin",fileSize:"2.3 MB",dimensions:"1920x1080",location:"Cebu City"},{id:"2",url:"/api/placeholder/400/300",title:"Anniversary Celebration",description:"Nag-celebrate mi sa first year sa business",date:"2024-02-20",likes:8,isLiked:!1,category:"celebrations",tags:["anniversary","celebration","milestone"],views:32,isPrivate:!1,uploadedBy:"Admin",fileSize:"1.8 MB",dimensions:"1920x1080",location:"Cebu City"},{id:"3",url:"/api/placeholder/400/300",title:"Community Festival",description:"Nag-participate mi sa local community festival",date:"2024-03-10",likes:15,isLiked:!0,category:"events",tags:["community","festival","participation"],views:67,isPrivate:!1,uploadedBy:"Admin",fileSize:"3.1 MB",dimensions:"1920x1080",location:"Barangay San Jose"},{id:"4",url:"/api/placeholder/400/300",title:"Mga Produkto sa Store",description:"Showcase sa mga bag-ong produkto sa aming tindahan",date:"2024-03-25",likes:6,isLiked:!1,category:"products",tags:["products","showcase","inventory"],views:28,isPrivate:!1,uploadedBy:"Admin",fileSize:"1.5 MB",dimensions:"1920x1080"},{id:"5",url:"/api/placeholder/400/300",title:"Family Bonding Time",description:"Quality time sa pamilya after work sa tindahan",date:"2024-04-05",likes:20,isLiked:!0,category:"family",tags:["family","bonding","quality-time"],views:89,isPrivate:!1,uploadedBy:"Admin",fileSize:"2.7 MB",dimensions:"1920x1080"},{id:"6",url:"/api/placeholder/400/300",title:"Mga Suki sa Tindahan",description:"Mga loyal customers nga nag-visit sa store",date:"2024-04-12",likes:11,isLiked:!1,category:"customers",tags:["customers","loyalty","community"],views:41,isPrivate:!1,uploadedBy:"Admin",fileSize:"2.1 MB",dimensions:"1920x1080"}]),[s,i]=(0,l.useState)(!1),[n,x]=(0,l.useState)(null),[m,g]=(0,l.useState)(!1),[u,h]=(0,l.useState)(0),[p,b]=(0,l.useState)(!1),[y,f]=(0,l.useState)(""),[j,v]=(0,l.useState)("all"),[C,S]=(0,l.useState)("date"),[D,L]=(0,l.useState)("desc"),[E,R]=(0,l.useState)("grid"),[I,z]=(0,l.useState)([]),[F,B]=(0,l.useState)(!1),[U,O]=(0,l.useState)(!1),H=(0,l.useRef)(null),[q,W]=(0,l.useState)({title:"",description:"",file:null,category:"family",tags:"",isPrivate:!1,location:""}),G=(0,l.useMemo)(()=>{let e=a.filter(e=>{let a=""===y||e.title.toLowerCase().includes(y.toLowerCase())||e.description.toLowerCase().includes(y.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(y.toLowerCase())),t="all"===j||e.category===j;return a&&t});return e.sort((e,a)=>{let t=0;switch(C){case"date":t=new Date(e.date).getTime()-new Date(a.date).getTime();break;case"likes":t=e.likes-a.likes;break;case"views":t=e.views-a.views;break;case"title":t=e.title.localeCompare(a.title)}return"desc"===D?-t:t}),e},[a,y,j,C,D]),K=e=>{t(a.map(a=>a.id===e?{...a,likes:a.isLiked?a.likes-1:a.likes+1,isLiked:!a.isLiked}:a))},J=e=>{t(a.map(a=>a.id===e?{...a,views:a.views+1}:a))},Q=e=>{confirm("Sigurado ka ba nga i-delete ni nga photo?")&&(t(a.filter(a=>a.id!==e)),z(I.filter(a=>a!==e)))},X=e=>{z(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},er=e=>{h(e),g(!0),J(G[e].id)},es=(0,l.useMemo)(()=>{let e=a.reduce((e,a)=>e+a.views,0),t=a.reduce((e,a)=>e+a.likes,0),r=a.reduce((e,a)=>(e[a.category]=(e[a.category]||0)+1,e),{});return{totalPhotos:a.length,totalViews:e,totalLikes:t,categoryCounts:r,averageViews:Math.round(e/a.length)||0,mostPopular:a.reduce((e,a)=>e.views>a.views?e:a,a[0])}},[a]),el=[{id:"all",label:"Tanan",icon:eS.A},{id:"family",label:"Pamilya",icon:d.A},{id:"store",label:"Tindahan",icon:eD.A},{id:"events",label:"Mga Event",icon:k.A},{id:"products",label:"Produkto",icon:eP.A},{id:"customers",label:"Mga Suki",icon:eM.A},{id:"celebrations",label:"Selebrasyon",icon:eT.A}];return(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-r from-amber-600 to-yellow-700 rounded-lg shadow-lg",children:(0,r.jsx)(eL.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Family Gallery"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Mga memories ug moments sa inyong pamilya ug tindahan"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mt-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-blue-600 dark:text-blue-400",children:"Total Photos"}),(0,r.jsx)("p",{className:"text-lg font-bold text-blue-900 dark:text-blue-300",children:es.totalPhotos})]}),(0,r.jsx)(o.A,{className:"h-5 w-5 text-blue-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-3 rounded-lg border border-green-200 dark:border-green-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-green-600 dark:text-green-400",children:"Total Views"}),(0,r.jsx)("p",{className:"text-lg font-bold text-green-900 dark:text-green-300",children:es.totalViews})]}),(0,r.jsx)(Z.A,{className:"h-5 w-5 text-green-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-3 rounded-lg border border-red-200 dark:border-red-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-red-600 dark:text-red-400",children:"Total Likes"}),(0,r.jsx)("p",{className:"text-lg font-bold text-red-900 dark:text-red-300",children:es.totalLikes})]}),(0,r.jsx)(eM.A,{className:"h-5 w-5 text-red-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-amber-50 to-yellow-100 dark:from-amber-900/20 dark:to-yellow-800/20 p-3 rounded-lg border border-amber-200 dark:border-amber-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-amber-700 dark:text-amber-400",children:"Selected"}),(0,r.jsx)("p",{className:"text-lg font-bold text-amber-900 dark:text-amber-300",children:I.length})]}),(0,r.jsx)(P.A,{className:"h-5 w-5 text-amber-600"})]})})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[I.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{onClick:()=>{I.length>0&&confirm("Sigurado ka ba nga i-delete ang ".concat(I.length," ka photos?"))&&(t(a.filter(e=>!I.includes(e.id))),z([]))},className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all",children:[(0,r.jsx)(ee.A,{className:"h-4 w-4"}),"Delete Selected (",I.length,")"]}),(0,r.jsxs)("button",{onClick:()=>z([]),className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all",children:[(0,r.jsx)(ea.A,{className:"h-4 w-4"}),"Clear Selection"]})]}),(0,r.jsxs)("button",{onClick:()=>O(!U),className:"btn-outline flex items-center gap-2 text-sm",disabled:U,children:[(0,r.jsx)(T.A,{className:"h-4 w-4 ".concat(U?"animate-spin":"")}),"Refresh"]}),(0,r.jsxs)("button",{onClick:()=>i(!0),className:"btn-primary flex items-center gap-2",children:[(0,r.jsx)(_.A,{className:"h-4 w-4"}),"Add Photo"]})]})]}),(0,r.jsxs)("div",{className:"card p-6 border-l-4 border-l-amber-600",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-amber-600"}),"Search & Filters"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("button",{onClick:()=>B(!F),className:"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white flex items-center gap-1 transition-colors",children:[(0,r.jsx)(Y.A,{className:"h-4 w-4"}),F?"Hide Filters":"Show Filters"]}),(0,r.jsxs)("div",{className:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1",children:[(0,r.jsx)("button",{onClick:()=>R("grid"),className:"px-3 py-1 text-xs font-medium rounded-md transition-all ".concat("grid"===E?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"),children:(0,r.jsx)(eS.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>R("list"),className:"px-3 py-1 text-xs font-medium rounded-md transition-all ".concat("list"===E?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"),children:(0,r.jsx)(eE.A,{className:"h-4 w-4"})})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search photos, descriptions, tags...",value:y,onChange:e=>f(e.target.value),className:"w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700 text-sm"})]}),(0,r.jsx)("select",{value:j,onChange:e=>v(e.target.value),className:"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700 text-sm",children:el.map(e=>(0,r.jsx)("option",{value:e.id,children:e.label},e.id))}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("select",{value:C,onChange:e=>S(e.target.value),className:"flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700 text-sm",children:[(0,r.jsx)("option",{value:"date",children:"Sort by Date"}),(0,r.jsx)("option",{value:"likes",children:"Sort by Likes"}),(0,r.jsx)("option",{value:"views",children:"Sort by Views"}),(0,r.jsx)("option",{value:"title",children:"Sort by Title"})]}),(0,r.jsx)("button",{onClick:()=>L("asc"===D?"desc":"asc"),className:"px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",title:"Sort ".concat("asc"===D?"Descending":"Ascending"),children:"asc"===D?(0,r.jsx)(eR.A,{className:"h-4 w-4"}):(0,r.jsx)(e_.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"flex gap-2",children:(0,r.jsx)("button",{onClick:()=>{I.length===G.length?z([]):z(G.map(e=>e.id))},className:"flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-sm",children:I.length===G.length?"Deselect All":"Select All"})})]}),F&&(0,r.jsx)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4 animate-fade-in-up",children:(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:el.slice(1).map(e=>{let a=e.icon,t=es.categoryCounts[e.id]||0;return(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("button",{onClick:()=>v(e.id),className:"w-full p-3 rounded-lg border-2 transition-all ".concat(j===e.id?"border-amber-600 bg-amber-50 dark:bg-amber-900/20":"border-gray-200 dark:border-gray-700 hover:border-amber-300"),children:[(0,r.jsx)(a,{className:"h-6 w-6 mx-auto mb-2 ".concat(j===e.id?"text-amber-600":"text-gray-500")}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.label}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[t," photos"]})]})},e.id)})})})]}),(0,r.jsxs)("div",{className:"card overflow-hidden",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-amber-600"}),"Photo Gallery (",G.length,")"]}),I.length>0&&(0,r.jsxs)("span",{className:"text-xs bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 px-2 py-1 rounded-full",children:[I.length," selected"]})]})}),"grid"===E?(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:G.map((e,a)=>{var t;return(0,r.jsxs)("div",{className:"group relative",children:[(0,r.jsx)("div",{className:"absolute top-2 left-2 z-10",children:(0,r.jsx)("input",{type:"checkbox",checked:I.includes(e.id),onChange:()=>X(e.id),className:"w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2"})}),(0,r.jsx)("div",{className:"absolute top-2 right-2 z-10",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("family"===e.category?"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400":"store"===e.category?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"events"===e.category?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"products"===e.category?"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400":"customers"===e.category?"bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400":"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"),children:null==(t=el.find(a=>a.id===e.category))?void 0:t.label})}),(0,r.jsxs)("div",{className:"card overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1",children:[(0,r.jsxs)("div",{className:"relative aspect-video bg-gray-200 dark:bg-gray-700 cursor-pointer",onClick:()=>er(a),children:[(0,r.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 flex items-center justify-center",children:(0,r.jsx)(o.A,{className:"h-16 w-16 text-gray-400"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("button",{onClick:e=>{e.stopPropagation(),er(a)},className:"bg-white text-gray-900 px-3 py-2 rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors",children:[(0,r.jsx)(Z.A,{className:"h-4 w-4 inline mr-1"}),"View"]}),(0,r.jsxs)("button",{onClick:a=>{a.stopPropagation(),x(e)},className:"bg-white text-gray-900 px-3 py-2 rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 inline mr-1"}),"Details"]})]})}),(0,r.jsxs)("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-xs flex items-center gap-1",children:[(0,r.jsx)(Z.A,{className:"h-3 w-3"}),e.views]})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white mb-1 line-clamp-1",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2",children:e.description}),e.tags.length>0&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 mb-3",children:[e.tags.slice(0,3).map(e=>(0,r.jsxs)("span",{className:"inline-flex px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-md",children:["#",e]},e)),e.tags.length>3&&(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["+",e.tags.length-3," more"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsx)(M.A,{className:"h-3 w-3"}),new Date(e.date).toLocaleDateString("en-PH")]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>K(e.id),className:"flex items-center space-x-1 text-sm transition-colors ".concat(e.isLiked?"text-red-500":"text-gray-500 dark:text-gray-400 hover:text-red-500"),children:[(0,r.jsx)(eM.A,{className:"h-4 w-4 ".concat(e.isLiked?"fill-current":"")}),(0,r.jsx)("span",{children:e.likes})]}),(0,r.jsx)("button",{className:"text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors",children:(0,r.jsx)(eI.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("button",{className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors",children:(0,r.jsx)(ez.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"absolute right-0 top-full mt-1 w-32 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1 opacity-0 group-hover:opacity-100 transition-opacity z-10",children:[(0,r.jsxs)("button",{onClick:()=>x(e),className:"w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center gap-2",children:[(0,r.jsx)($.A,{className:"h-3 w-3"}),"Edit"]}),(0,r.jsxs)("button",{onClick:()=>Q(e.id),className:"w-full text-left px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center gap-2",children:[(0,r.jsx)(ee.A,{className:"h-3 w-3"}),"Delete"]})]})]})]})]})]})]})]},e.id)})})}):(0,r.jsx)("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:G.map((e,a)=>{var t;return(0,r.jsx)("div",{className:"p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 group",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("input",{type:"checkbox",checked:I.includes(e.id),onChange:()=>X(e.id),className:"mt-1 w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2"}),(0,r.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg flex items-center justify-center cursor-pointer",onClick:()=>er(a),children:(0,r.jsx)(o.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsx)("div",{className:"flex-1 min-w-0",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-1",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(k.A,{className:"h-3 w-3"}),new Date(e.date).toLocaleDateString("en-PH")]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(Z.A,{className:"h-3 w-3"}),e.views," views"]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(eM.A,{className:"h-3 w-3"}),e.likes," likes"]}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("family"===e.category?"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400":"store"===e.category?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"events"===e.category?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"products"===e.category?"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400":"customers"===e.category?"bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400":"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"),children:null==(t=el.find(a=>a.id===e.category))?void 0:t.label})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>er(a),className:"text-gray-500 dark:text-gray-400 hover:text-amber-600 transition-colors",children:(0,r.jsx)(Z.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>K(e.id),className:"transition-colors ".concat(e.isLiked?"text-red-500":"text-gray-500 dark:text-gray-400 hover:text-red-500"),children:(0,r.jsx)(eM.A,{className:"h-4 w-4 ".concat(e.isLiked?"fill-current":"")})}),(0,r.jsx)("button",{className:"text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors",children:(0,r.jsx)(eI.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>Q(e.id),className:"text-gray-500 dark:text-gray-400 hover:text-red-500 transition-colors",children:(0,r.jsx)(ee.A,{className:"h-4 w-4"})})]})]})})]})},e.id)})}),0===G.length&&(0,r.jsxs)("div",{className:"p-16 text-center",children:[(0,r.jsx)("div",{className:"bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(o.A,{className:"h-12 w-12 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-3",children:"Walang Photos na Nakita"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto",children:"Try adjusting your search terms or filter criteria to find the photos you're looking for."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,r.jsx)("button",{onClick:()=>{f(""),v("all")},className:"btn-outline",children:"Clear Filters"}),(0,r.jsxs)("button",{onClick:()=>i(!0),className:"btn-primary",children:[(0,r.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"Add First Photo"]})]})]})]}),s&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-3 bg-gradient-to-r from-amber-600 to-yellow-700 rounded-xl shadow-lg",children:(0,r.jsx)(et.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Add New Photo"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Upload a new photo to your family gallery"})]})]}),(0,r.jsx)("button",{onClick:()=>i(!1),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,r.jsx)(ea.A,{className:"h-5 w-5 text-gray-500"})})]})}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),q.file&&q.title&&(O(!0),setTimeout(()=>{t([{id:Date.now().toString(),url:URL.createObjectURL(q.file),title:q.title,description:q.description,date:new Date().toISOString().split("T")[0]||"",likes:0,isLiked:!1,category:q.category,tags:q.tags.split(",").map(e=>e.trim()).filter(e=>e),views:0,isPrivate:q.isPrivate,uploadedBy:"Admin",fileSize:"".concat((q.file.size/1048576).toFixed(1)," MB"),dimensions:"1920x1080",location:q.location},...a]),W({title:"",description:"",file:null,category:"family",tags:"",isPrivate:!1,location:""}),i(!1),O(!1)},1500))},className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Photo File"}),(0,r.jsx)("div",{onDragOver:e=>{e.preventDefault()},onDrop:e=>{e.preventDefault();let a=e.dataTransfer.files[0];a&&a.type.startsWith("image/")&&W({...q,file:a})},onClick:()=>{var e;return null==(e=H.current)?void 0:e.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-amber-400 dark:hover:border-amber-500 transition-colors",children:q.file?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.A,{className:"h-12 w-12 text-amber-600 mx-auto"}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:q.file.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(q.file.size/1048576).toFixed(1)," MB"]}),(0,r.jsx)("button",{type:"button",onClick:e=>{e.stopPropagation(),W({...q,file:null})},className:"text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",children:"Remove file"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(et.A,{className:"h-12 w-12 text-gray-400 mx-auto"}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Click to upload or drag and drop"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,r.jsx)("input",{ref:H,type:"file",accept:"image/*",onChange:e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];t&&W({...q,file:t})},className:"hidden",required:!0})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Title"}),(0,r.jsx)("input",{type:"text",value:q.title,onChange:e=>W({...q,title:e.target.value}),placeholder:"Enter photo title...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Category"}),(0,r.jsx)("select",{value:q.category,onChange:e=>W({...q,category:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700",children:el.slice(1).map(e=>(0,r.jsx)("option",{value:e.id,children:e.label},e.id))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Description"}),(0,r.jsx)("textarea",{value:q.description,onChange:e=>W({...q,description:e.target.value}),rows:4,placeholder:"Describe your photo...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Tags (comma separated)"}),(0,r.jsx)("input",{type:"text",value:q.tags,onChange:e=>W({...q,tags:e.target.value}),placeholder:"family, celebration, milestone...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Location (optional)"}),(0,r.jsx)("input",{type:"text",value:q.location,onChange:e=>W({...q,location:e.target.value}),placeholder:"Cebu City, Philippines...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"isPrivate",checked:q.isPrivate,onChange:e=>W({...q,isPrivate:e.target.checked}),className:"w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2"}),(0,r.jsx)("label",{htmlFor:"isPrivate",className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"Make this photo private (only visible to family members)"})]}),(0,r.jsxs)("div",{className:"flex space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("button",{type:"button",onClick:()=>i(!1),className:"flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 font-medium transition-colors",disabled:U,children:"Cancel"}),(0,r.jsx)("button",{type:"submit",className:"flex-1 bg-gradient-to-r from-amber-600 to-yellow-700 hover:from-amber-700 hover:to-yellow-800 text-white px-6 py-3 rounded-lg font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 shadow-lg",disabled:U,children:U?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(T.A,{className:"h-4 w-4 animate-spin"}),"Uploading..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(et.A,{className:"h-4 w-4"}),"Upload Photo"]})})]})]})]})}),m&&G[u]&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-50 animate-fade-in",children:(0,r.jsxs)("div",{className:"relative w-full h-full flex items-center justify-center p-4",children:[(0,r.jsx)("button",{onClick:()=>{g(!1),b(!1)},className:"absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all",children:(0,r.jsx)(ea.A,{className:"h-6 w-6"})}),G.length>1&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>{let e=0===u?G.length-1:u-1;h(e),J(G[e].id)},className:"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all",children:(0,r.jsx)(A.A,{className:"h-8 w-8"})}),(0,r.jsx)("button",{onClick:()=>{let e=(u+1)%G.length;h(e),J(G[e].id)},className:"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all",children:(0,r.jsx)(w.A,{className:"h-8 w-8"})})]}),(0,r.jsxs)("div",{className:"absolute top-4 left-4 z-10 flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>{b(!p)},className:"p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all",children:p?(0,r.jsx)(eF.A,{className:"h-5 w-5"}):(0,r.jsx)(eB.A,{className:"h-5 w-5"})}),(0,r.jsxs)("span",{className:"text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full",children:[u+1," / ",G.length]})]}),(0,r.jsx)("div",{className:"max-w-4xl max-h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"relative bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg flex items-center justify-center",style:{minWidth:"600px",minHeight:"400px"},children:[(0,r.jsx)(o.A,{className:"h-32 w-32 text-gray-400"}),(0,r.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6 text-white",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:G[u].title}),(0,r.jsx)("p",{className:"text-sm opacity-90 mb-3",children:G[u].description}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(k.A,{className:"h-4 w-4"}),new Date(G[u].date).toLocaleDateString("en-PH")]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(Z.A,{className:"h-4 w-4"}),G[u].views]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(eM.A,{className:"h-4 w-4"}),G[u].likes]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>K(G[u].id),className:"p-2 rounded-full transition-all ".concat(G[u].isLiked?"bg-red-500 text-white":"bg-white bg-opacity-20 text-white hover:bg-opacity-30"),children:(0,r.jsx)(eM.A,{className:"h-4 w-4 ".concat(G[u].isLiked?"fill-current":"")})}),(0,r.jsx)("button",{className:"p-2 bg-white bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-all",children:(0,r.jsx)(eI.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"p-2 bg-white bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-all",children:(0,r.jsx)(V.A,{className:"h-4 w-4"})})]})]})]})]})})]})}),n&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-3 rounded-xl shadow-lg ".concat("family"===n.category?"bg-blue-500":"store"===n.category?"bg-green-500":"events"===n.category?"bg-yellow-500":"products"===n.category?"bg-amber-600":"customers"===n.category?"bg-pink-500":"bg-orange-500"),children:(0,r.jsx)(o.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Photo Details"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:n.title})]})]}),(0,r.jsx)("button",{onClick:()=>x(null),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,r.jsx)(ea.A,{className:"h-5 w-5 text-gray-500"})})]})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"aspect-video bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg mb-4 flex items-center justify-center",children:(0,r.jsx)(o.A,{className:"h-24 w-24 text-gray-400"})}),(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("button",{onClick:()=>K(n.id),className:"flex items-center gap-2 px-4 py-2 rounded-lg transition-all ".concat(n.isLiked?"bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400":"bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 hover:bg-red-50 hover:text-red-600"),children:[(0,r.jsx)(eM.A,{className:"h-4 w-4 ".concat(n.isLiked?"fill-current":"")}),n.likes]}),(0,r.jsxs)("button",{className:"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all",children:[(0,r.jsx)(eI.A,{className:"h-4 w-4"}),"Share"]}),(0,r.jsxs)("button",{className:"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-lg hover:bg-green-50 hover:text-green-600 transition-all",children:[(0,r.jsx)(V.A,{className:"h-4 w-4"}),"Download"]})]})})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Information"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Title"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white mt-1",children:n.title})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Description"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white mt-1",children:n.description||"No description provided"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Category"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white mt-1",children:null==(e=el.find(e=>e.id===n.category))?void 0:e.label})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Date"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white mt-1",children:new Date(n.date).toLocaleDateString("en-PH")})]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Statistics"}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,r.jsx)(Z.A,{className:"h-5 w-5 text-blue-500 mx-auto mb-1"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900 dark:text-white",children:n.views}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Views"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,r.jsx)(eM.A,{className:"h-5 w-5 text-red-500 mx-auto mb-1"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900 dark:text-white",children:n.likes}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Likes"})]}),(0,r.jsxs)("div",{className:"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,r.jsx)(eI.A,{className:"h-5 w-5 text-green-500 mx-auto mb-1"}),(0,r.jsx)("p",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"0"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Shares"})]})]})]}),n.tags.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Tags"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:n.tags.map(e=>(0,r.jsxs)("span",{className:"inline-flex px-3 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-400 text-sm rounded-full",children:["#",e]},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Technical Details"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"File Size:"}),(0,r.jsx)("span",{className:"text-gray-900 dark:text-white",children:n.fileSize})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Dimensions:"}),(0,r.jsx)("span",{className:"text-gray-900 dark:text-white",children:n.dimensions})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Uploaded by:"}),(0,r.jsx)("span",{className:"text-gray-900 dark:text-white",children:n.uploadedBy})]}),n.location&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Location:"}),(0,r.jsx)("span",{className:"text-gray-900 dark:text-white",children:n.location})]})]})]})]})]})})]})})]})}var eO=t(4516);let eH={calendar:"Kalendaryo",today:"Karon nga Adlaw",events:"Mga Panghitabo",schedule:"Iskedyul",moonPhases:"Mga Hugis sa Bulan",moonPhase:"Hugis sa Bulan",newMoon:"Bag-ong Bulan",waxingCrescent:"Nagdako nga Sungay",firstQuarter:"Una nga Bahin",waxingGibbous:"Nagdako nga Bula",fullMoon:"Puno nga Bulan",waningGibbous:"Nagliit nga Bula",lastQuarter:"Katapusan nga Bahin",waningCrescent:"Nagliit nga Sungay",newMoonDesc:"Ang bulan dili makita gikan sa yuta",waxingCrescentDesc:"Nipis nga sungay sa bulan sa tuo nga bahin",firstQuarterDesc:"Katunga sa bulan nag-hayag sa tuo nga bahin",waxingGibbousDesc:"Sobra sa katunga sa bulan nag-hayag",fullMoonDesc:"Tibuok nga bulan nag-hayag ug makita",waningGibbousDesc:"Sobra sa katunga nag-hayag, nagliit na",lastQuarterDesc:"Katunga sa bulan nag-hayag sa wala nga bahin",waningCrescentDesc:"Nipis nga sungay sa bulan sa wala nga bahin",addEvent:"Dugang Event",manage:"Pagdumala",upcoming:"Umaabot na",legend:"Giya",age:"Edad",illumination:"Kahayag",days:"mga adlaw",next:"Sunod",cancel:"Kanselar",description:"Deskripsyon",location:"Lugar",time:"Oras",date:"Petsa",title:"Titulo",type:"Klase",attendees:"Mga Apil",more:"pa",delivery:"Delivery",meeting:"Meeting",reminder:"Pahinumdom",holiday:"Holiday",personal:"Personal"},eq=()=>[{name:eH.newMoon,emoji:"\uD83C\uDF11",icon:"new-moon",illumination:0,description:eH.newMoonDesc},{name:eH.waxingCrescent,emoji:"\uD83C\uDF12",icon:"waxing-crescent",illumination:25,description:eH.waxingCrescentDesc},{name:eH.firstQuarter,emoji:"\uD83C\uDF13",icon:"first-quarter",illumination:50,description:eH.firstQuarterDesc},{name:eH.waxingGibbous,emoji:"\uD83C\uDF14",icon:"waxing-gibbous",illumination:75,description:eH.waxingGibbousDesc},{name:eH.fullMoon,emoji:"\uD83C\uDF15",icon:"full-moon",illumination:100,description:eH.fullMoonDesc},{name:eH.waningGibbous,emoji:"\uD83C\uDF16",icon:"waning-gibbous",illumination:75,description:eH.waningGibbousDesc},{name:eH.lastQuarter,emoji:"\uD83C\uDF17",icon:"last-quarter",illumination:50,description:eH.lastQuarterDesc},{name:eH.waningCrescent,emoji:"\uD83C\uDF18",icon:"waning-crescent",illumination:25,description:eH.waningCrescentDesc}],eW=e=>{let a=new Date(2e3,0,6,18,14),t=(e.getTime()-a.getTime())/864e5%29.53058867,r=t<0?t+29.53058867:t,s=Math.round((1-Math.cos(r/29.53058867*2*Math.PI))*50),l=eq(),i=0;i=Math.max(0,Math.min(i=r<1.84566?0:r<5.53699?1:r<9.22831?2:r<12.91963?3:r<16.61096?4:r<20.30228?5:r<23.99361?6:7,l.length-1));let n=new Date(e.getTime()+(14.76529-r+29.53058867)%29.53058867*864e5),d=new Date(e.getTime()+(29.53058867-r)%29.53058867*864e5);return{phase:l[i],age:Math.round(10*r)/10,illumination:s,nextFullMoon:n,nextNewMoon:d}},eY=e=>{let{phase:a,size:t=16,className:s=""}=e;return(0,r.jsx)("span",{className:"moon-phase-icon ".concat(s),style:{width:t,height:t,fontSize:t,display:"inline-block",lineHeight:1},title:"".concat(a.name," - ").concat(a.description),children:a.emoji})},eV=e=>{let{moonData:a,className:t=""}=e;return(0,r.jsxs)("div",{className:"absolute z-10 p-3 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 min-w-48 ".concat(t),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(eY,{phase:a.phase,size:20}),(0,r.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:a.phase.name})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,r.jsx)("p",{children:a.phase.description}),(0,r.jsxs)("p",{children:[eH.age,": ",a.age," ",eH.days]}),(0,r.jsxs)("p",{children:[eH.illumination,": ",a.illumination,"%"]}),(0,r.jsxs)("p",{className:"text-xs pt-1 border-t border-gray-200 dark:border-gray-600",children:[eH.next," ",eH.fullMoon,": ",a.nextFullMoon.toLocaleDateString("tl-PH")]})]})]})};function eG(){let[e,a]=(0,l.useState)(new Date),[t,s]=(0,l.useState)(null),[i,n]=(0,l.useState)(!1),[o,c]=(0,l.useState)(!0),[x,g]=(0,l.useState)(null),[u,h]=(0,l.useState)([{id:"1",title:"Supplier Delivery",description:"Weekly grocery delivery from main supplier",date:"2024-01-22",time:"09:00",type:"delivery",location:"Store Front"},{id:"2",title:"Monthly Inventory Check",description:"Complete inventory count and stock verification",date:"2024-01-25",time:"14:00",type:"reminder"},{id:"3",title:"Community Meeting",description:"Barangay business owners meeting",date:"2024-01-28",time:"16:00",type:"meeting",location:"Barangay Hall",attendees:["Maria Santos","Juan Dela Cruz","Ana Reyes"]},{id:"4",title:"New Year Holiday",description:"Store closed for New Year celebration",date:"2024-01-01",time:"00:00",type:"holiday"}]),[p,b]=(0,l.useState)({title:"",description:"",date:"",time:"",type:"reminder",location:""}),y=e=>{let a=e.toISOString().split("T")[0];return u.filter(e=>e.date===a)},f=e=>{switch(e){case"delivery":return"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";case"meeting":return"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400";case"reminder":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";case"holiday":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";case"personal":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"}},j=e=>{a(a=>{let t=new Date(a);return"prev"===e?t.setMonth(a.getMonth()-1):t.setMonth(a.getMonth()+1),t})},v=(e=>{let a=e.getFullYear(),t=e.getMonth(),r=new Date(a,t,1),s=new Date(a,t+1,0).getDate(),l=r.getDay(),i=[];for(let e=0;e<l;e++)i.push(null);for(let e=1;e<=s;e++)i.push(new Date(a,t,e));return i})(e),N=new Date;return(0,r.jsxs)("div",{className:"space-y-6 bisaya-calendar",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white cultural-accent bisaya-text",children:eH.calendar}),(0,r.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mt-1 bisaya-text",children:[eH.manage," sa inyong store ",eH.events," ug ",eH.schedule," uban sa lunar phases"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("button",{onClick:()=>c(!o),className:"flex items-center px-3 py-2 rounded-lg border transition-all duration-200 hover:scale-105 ".concat(o?"bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-900/30 dark:border-blue-600 dark:text-blue-400 shadow-md":"border-gray-300 text-gray-600 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-slate-700"),children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),eH.moonPhases]}),(0,r.jsxs)("button",{onClick:()=>n(!0),className:"btn-primary flex items-center hover:scale-105 shadow-lg",children:[(0,r.jsx)(_.A,{className:"h-4 w-4 mr-2"}),eH.addEvent]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[["Enero","Pebrero","Marso","Abril","Mayo","Hunyo","Hulyo","Agosto","Septyembre","Oktubre","Nobyembre","Disyembre"][e.getMonth()]," ",e.getFullYear()]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>j("prev"),className:"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700",children:(0,r.jsx)(A.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>a(new Date),className:"px-4 py-2 text-sm bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-all duration-200 hover:scale-105 shadow-sm",children:eH.today}),(0,r.jsx)("button",{onClick:()=>j("next"),className:"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-7 gap-1",children:[["Dom","Lun","Mar","Miy","Huw","Biy","Sab"].map(e=>(0,r.jsx)("div",{className:"p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400",children:e},e)),v.map((e,a)=>{if(!e)return(0,r.jsx)("div",{className:"p-3 h-28"},a);let l=y(e),i=e.toDateString()===N.toDateString(),n=(null==t?void 0:t.toDateString())===e.toDateString(),d=eW(e);return(0,r.jsxs)("div",{onClick:()=>s(e),className:"calendar-day-cell p-2 h-28 border border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 transition-all duration-200 relative ".concat(i?"bg-green-50 dark:bg-green-900/20 ring-1 ring-green-300 dark:ring-green-600":""," ").concat(n?"ring-2 ring-green-500 bg-green-100 dark:bg-green-900/30":""),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("div",{className:"text-sm font-medium ".concat(i?"text-green-600 dark:text-green-400":"text-gray-900 dark:text-white"),children:e.getDate()}),o&&(0,r.jsx)("div",{className:"moon-phase-container relative",onMouseEnter:a=>{let t=a.currentTarget.getBoundingClientRect();g({date:e,moonData:d,position:{x:t.left,y:t.top}})},onMouseLeave:()=>g(null),children:(0,r.jsx)(eY,{phase:d.phase,size:14,className:"opacity-80 hover:opacity-100 transition-opacity duration-200"})})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[l.slice(0,o?1:2).map(e=>(0,r.jsx)("div",{className:"text-xs px-1 py-0.5 rounded truncate ".concat(f(e.type)),children:e.title},e.id)),l.length>(o?1:2)&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["+",l.length-(o?1:2)," ",eH.more]})]})]},a)})]})]}),o&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2 text-blue-500"}),eH.moonPhase," Karon nga Adlaw"]}),(()=>{let e=eW(N);return(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsx)(eY,{phase:e.phase,size:48,className:"mx-auto"})}),(0,r.jsx)("h4",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:e.phase.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:e.phase.description}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-slate-700/50 p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:eH.age}),(0,r.jsxs)("div",{className:"text-gray-600 dark:text-gray-400",children:[e.age," ",eH.days]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-slate-700/50 p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:eH.illumination}),(0,r.jsxs)("div",{className:"text-gray-600 dark:text-gray-400",children:[e.illumination,"%"]})]})]})]})})()]}),(0,r.jsxs)("div",{className:"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text",children:[(0,r.jsx)("span",{className:"text-lg mr-2",children:"\uD83C\uDF19"}),eH.upcoming," nga Bulan ",eH.events]}),(()=>{let e=eW(N);return(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-xl",children:"\uD83C\uDF15"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900 dark:text-white",children:[eH.next," ",eH.fullMoon]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.nextFullMoon.toLocaleDateString("tl-PH",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})}),(0,r.jsx)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-slate-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-600/50 transition-colors duration-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-xl",children:"\uD83C\uDF11"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900 dark:text-white",children:[eH.next," ",eH.newMoon]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.nextNewMoon.toLocaleDateString("tl-PH",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]})})]})})()]}),(0,r.jsxs)("div",{className:"card p-4 animate-fade-in-up filipino-shadow transition-all duration-300",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center bisaya-text",children:[(0,r.jsx)("span",{className:"text-lg mr-2",children:"\uD83D\uDCD6"}),eH.legend," sa ",eH.moonPhases]}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-2",children:eq().map((e,a)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 hover:scale-102",children:[(0,r.jsx)(eY,{phase:e,size:18}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.illumination,"% ",eH.illumination]})]})]},a))})]})]}),x&&(0,r.jsx)("div",{className:"fixed pointer-events-none z-50",style:{left:x.position.x,top:x.position.y-10,transform:"translateY(-100%)"},children:(0,r.jsx)(eV,{moonData:x.moonData})}),(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-all duration-300",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)("span",{className:"text-lg mr-2",children:"\uD83D\uDCC5"}),eH.upcoming," nga ",eH.events]}),(0,r.jsx)("div",{className:"space-y-3",children:u.filter(e=>new Date(e.date)>=N).sort((e,a)=>new Date(e.date).getTime()-new Date(a.date).getTime()).slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg ".concat(f(e.type)),children:(0,r.jsx)(k.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(M.A,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:[new Date(e.date).toLocaleDateString()," at ",e.time]})]}),e.location&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(eO.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:e.location})]}),e.attendees&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(d.A,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:[e.attendees.length," ",eH.attendees]})]})]})]})]},e.id))})]}),i&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md shadow-2xl animate-fade-in-up",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(_.A,{className:"h-5 w-5 mr-2 text-green-500"}),"Dugang Bag-ong ",eH.events]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),p.title&&p.date&&p.time&&(h([...u,{id:Date.now().toString(),...p}]),b({title:"",description:"",date:"",time:"",type:"reminder",location:""}),n(!1))},className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[eH.title," sa ",eH.events]}),(0,r.jsx)("input",{type:"text",value:p.title,onChange:e=>b({...p,title:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",placeholder:"I-type ang titulo sa event...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:eH.description}),(0,r.jsx)("textarea",{value:p.description,onChange:e=>b({...p,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",placeholder:"Detalye sa event..."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:eH.date}),(0,r.jsx)("input",{type:"date",value:p.date,onChange:e=>b({...p,date:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:eH.time}),(0,r.jsx)("input",{type:"time",value:p.time,onChange:e=>b({...p,time:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",required:!0})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[eH.type," sa ",eH.events]}),(0,r.jsxs)("select",{value:p.type,onChange:e=>b({...p,type:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",children:[(0,r.jsx)("option",{value:"reminder",children:eH.reminder}),(0,r.jsx)("option",{value:"delivery",children:eH.delivery}),(0,r.jsx)("option",{value:"meeting",children:eH.meeting}),(0,r.jsx)("option",{value:"holiday",children:eH.holiday}),(0,r.jsx)("option",{value:"personal",children:eH.personal})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[eH.location," (Optional)"]}),(0,r.jsx)("input",{type:"text",value:p.location,onChange:e=>b({...p,location:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all duration-200",placeholder:"Asa ang event..."})]}),(0,r.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>n(!1),className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700 transition-all duration-200 hover:scale-105",children:eH.cancel}),(0,r.jsx)("button",{type:"submit",className:"flex-1 btn-primary hover:scale-105 shadow-lg",children:eH.addEvent})]})]})]})})]})}var eK=t(4213),eJ=t(5525),eZ=t(9022),eQ=t(5339),eX=t(9145),e$=t(6517),e0=t(4449),e1=t(6561),e2=t(6474),e5=t(7434),e4=t(8979),e3=t(5448);function e6(){let[e,a]=(0,l.useState)(""),[t,s]=(0,l.useState)("all"),[i,d]=(0,l.useState)("7days"),[o,x]=(0,l.useState)([]),[m,u]=(0,l.useState)(!1),[h,p]=(0,l.useState)(null),[b,y]=(0,l.useState)(!1),[f,v]=(0,l.useState)("list"),[k,A]=(0,l.useState)("timestamp"),[C,D]=(0,l.useState)("desc"),[L,E]=(0,l.useState)(!1),[_,I]=(0,l.useState)(new Date),[z,F]=(0,l.useState)("online"),[U,O]=(0,l.useState)({types:[],priorities:[],statuses:[],dateRange:"7days",users:[],categories:[]}),H=(0,l.useMemo)(()=>[{id:"1",type:"product",action:"Product Added",description:'Added "Lucky Me Pancit Canton" to product list',user:"Admin",timestamp:"2024-01-20T10:30:00Z",priority:"medium",status:"success",category:"Inventory Management",tags:["product","inventory","add"],details:{productName:"Lucky Me Pancit Canton",price:15,sku:"LMC001",quantity:50,supplier:"Lucky Me Foods"}},{id:"2",type:"debt",action:"Debt Recorded",description:"New debt record for Juan Dela Cruz",user:"Admin",timestamp:"2024-01-20T09:15:00Z",priority:"high",status:"warning",category:"Customer Management",tags:["debt","customer","record"],details:{customer:"Juan Dela Cruz",amount:45,dueDate:"2024-02-20",contactNumber:"09*********",address:"Barangay San Jose, Cebu City"}},{id:"3",type:"payment",action:"Payment Received",description:"Payment received from Maria Santos",user:"Admin",timestamp:"2024-01-19T16:45:00Z",priority:"medium",status:"success",category:"Financial Transaction",tags:["payment","customer","income"],details:{customer:"Maria Santos",amount:120,paymentMethod:"Cash",previousBalance:200,newBalance:80}},{id:"4",type:"product",action:"Stock Updated",description:"Updated stock quantity for Coca-Cola",user:"Admin",timestamp:"2024-01-19T14:20:00Z",priority:"low",status:"success",category:"Inventory Management",tags:["product","stock","update"],details:{productName:"Coca-Cola",oldStock:25,newStock:50,reason:"New delivery received",supplier:"Coca-Cola Bottlers Philippines"}},{id:"5",type:"login",action:"User Login",description:"Admin user logged into the system",user:"Admin",timestamp:"2024-01-19T08:00:00Z",priority:"low",status:"info",category:"Security",tags:["login","security","access"],ipAddress:"***********00",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",details:{ipAddress:"***********00",location:"Cebu City, Philippines",device:"Desktop - Windows 10",sessionDuration:"4 hours 30 minutes"}},{id:"6",type:"system",action:"Backup Created",description:"Automatic database backup completed",user:"System",timestamp:"2024-01-19T02:00:00Z",priority:"medium",status:"success",category:"System Maintenance",tags:["backup","database","maintenance"],details:{backupSize:"2.5MB",backupType:"Full Backup",location:"Cloud Storage",duration:"45 seconds",tablesBackedUp:8}},{id:"7",type:"debt",action:"Debt Updated",description:"Updated debt record for Ana Reyes",user:"Admin",timestamp:"2024-01-18T15:30:00Z",priority:"high",status:"warning",category:"Customer Management",tags:["debt","customer","update"],details:{customer:"Ana Reyes",oldAmount:75,newAmount:100,reason:"Additional purchase",dueDate:"2024-02-18",contactNumber:"09987654321"}},{id:"8",type:"product",action:"Product Deleted",description:'Removed "Expired Milk" from product list',user:"Admin",timestamp:"2024-01-18T11:10:00Z",priority:"high",status:"error",category:"Inventory Management",tags:["product","delete","expired"],details:{productName:"Expired Milk",reason:"Product expired",expiryDate:"2024-01-15",quantityRemoved:12,loss:180}},{id:"9",type:"security",action:"Failed Login Attempt",description:"Multiple failed login attempts detected",user:"Unknown",timestamp:"2024-01-18T03:45:00Z",priority:"critical",status:"error",category:"Security Alert",tags:["security","login","failed"],ipAddress:"*************",details:{attempts:5,ipAddress:"*************",location:"Unknown",blocked:!0,duration:"24 hours"}},{id:"10",type:"notification",action:"Low Stock Alert",description:"Stock level below minimum threshold for multiple products",user:"System",timestamp:"2024-01-17T18:00:00Z",priority:"high",status:"warning",category:"Inventory Alert",tags:["stock","alert","inventory"],details:{affectedProducts:["Rice 25kg","Cooking Oil 1L","Sugar 1kg"],minimumThreshold:10,currentStock:[5,3,7],recommendedOrder:[50,20,30]}}],[]);(0,l.useEffect)(()=>{let e;return L&&(e=setInterval(()=>{I(new Date),y(!0),setTimeout(()=>y(!1),1e3)},3e4)),()=>{e&&clearInterval(e)}},[L]),(0,l.useEffect)(()=>{let e=()=>F("online"),a=()=>F("offline");return window.addEventListener("online",e),window.addEventListener("offline",a),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",a)}},[]);let q=e=>{switch(e){case"product":return(0,r.jsx)(n.A,{className:"h-4 w-4"});case"debt":return(0,r.jsx)(S.A,{className:"h-4 w-4 text-red-500"});case"payment":return(0,r.jsx)(S.A,{className:"h-4 w-4 text-green-500"});case"login":return(0,r.jsx)(g.A,{className:"h-4 w-4"});case"system":return(0,r.jsx)(eK.A,{className:"h-4 w-4"});case"security":return(0,r.jsx)(eJ.A,{className:"h-4 w-4"});case"backup":return(0,r.jsx)(eZ.A,{className:"h-4 w-4"});case"notification":return(0,r.jsx)(B.A,{className:"h-4 w-4"});default:return(0,r.jsx)(P.A,{className:"h-4 w-4"})}},Y=e=>{switch(e){case"critical":return(0,r.jsx)(eQ.A,{className:"h-4 w-4 text-red-600"});case"high":return(0,r.jsx)(R.A,{className:"h-4 w-4 text-orange-500"});case"medium":return(0,r.jsx)(M.A,{className:"h-4 w-4 text-yellow-500"});case"low":return(0,r.jsx)(eT.A,{className:"h-4 w-4 text-blue-500"});default:return(0,r.jsx)(M.A,{className:"h-4 w-4 text-gray-500"})}},G=e=>{switch(e){case"product":return"bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800";case"debt":return"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 border border-red-200 dark:border-red-800";case"payment":return"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800";case"login":return"bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 border border-purple-200 dark:border-purple-800";case"system":default:return"bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400 border border-gray-200 dark:border-gray-800";case"security":return"bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400 border border-orange-200 dark:border-orange-800";case"backup":return"bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-800";case"notification":return"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800"}},K=e=>{switch(e){case"success":return"bg-green-50 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800";case"warning":return"bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800";case"error":return"bg-red-50 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800";case"info":return"bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800";default:return"bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800"}},J=e=>{switch(e){case"critical":return"bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400 dark:border-red-700";case"high":return"bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-700";case"medium":return"bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-700";case"low":return"bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-400 dark:border-blue-700";default:return"bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-900/30 dark:text-gray-400 dark:border-gray-700"}},Q=(0,l.useMemo)(()=>{let a=H.filter(a=>{let r=""===e||a.description.toLowerCase().includes(e.toLowerCase())||a.action.toLowerCase().includes(e.toLowerCase())||a.user.toLowerCase().includes(e.toLowerCase())||a.category.toLowerCase().includes(e.toLowerCase())||a.tags&&a.tags.some(a=>a.toLowerCase().includes(e.toLowerCase())),s="all"===t||a.type===t,l=0===U.types.length||U.types.includes(a.type),n=0===U.priorities.length||U.priorities.includes(a.priority),d=0===U.statuses.length||U.statuses.includes(a.status),o=0===U.users.length||U.users.includes(a.user),c=0===U.categories.length||U.categories.includes(a.category),x=new Date(a.timestamp),m=new Date,g=!0;switch(i){case"24hours":g=m.getTime()-x.getTime()<=864e5;break;case"7days":g=m.getTime()-x.getTime()<=6048e5;break;case"30days":g=m.getTime()-x.getTime()<=2592e6;break;case"90days":g=m.getTime()-x.getTime()<=7776e6;break;case"all":g=!0}return r&&s&&l&&n&&d&&o&&c&&g});return a.sort((e,a)=>{let t=0;switch(k){case"timestamp":t=new Date(e.timestamp).getTime()-new Date(a.timestamp).getTime();break;case"type":t=e.type.localeCompare(a.type);break;case"priority":let r={critical:4,high:3,medium:2,low:1};t=(r[e.priority]||0)-(r[a.priority]||0)}return"desc"===C?-t:t}),a},[H,e,t,U,i,k,C]),X=[...new Set(H.map(e=>e.priority))],$=[...new Set(H.map(e=>e.status))],et=[...new Set(H.map(e=>e.user))],er=e=>{let a=new Date(e),t=Math.floor((new Date().getTime()-a.getTime())/6e4),r=Math.floor(t/60),s=Math.floor(r/24);return t<1?"Just now":t<60?"".concat(t," minute").concat(t>1?"s":""," ago"):r<24?"".concat(r," hour").concat(r>1?"s":""," ago"):s<7?"".concat(s," day").concat(s>1?"s":""," ago"):a.toLocaleDateString("en-PH",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},es=e=>{x(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},el=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Q,a=new Blob([[["Timestamp","Type","Action","Description","User","Priority","Status","Category"],...e.map(e=>[e.timestamp,e.type,e.action,e.description,e.user,e.priority,e.status,e.category])].map(e=>e.join(",")).join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(a),r=document.createElement("a");r.href=t,r.download="revantad-store-history-".concat(new Date().toISOString().split("T")[0],".csv"),r.click(),window.URL.revokeObjectURL(t)},ei=(0,l.useMemo)(()=>{let e={total:Q.length,byType:{},byPriority:{},byStatus:{},byUser:{},todayCount:0,weekCount:0},a=new Date,t=new Date(a.getFullYear(),a.getMonth(),a.getDate()),r=new Date(a.getTime()-6048e5);return Q.forEach(a=>{e.byType[a.type]=(e.byType[a.type]||0)+1,e.byPriority[a.priority]=(e.byPriority[a.priority]||0)+1,e.byStatus[a.status]=(e.byStatus[a.status]||0)+1,e.byUser[a.user]=(e.byUser[a.user]||0)+1;let s=new Date(a.timestamp);s>=t&&e.todayCount++,s>=r&&e.weekCount++}),e},[Q]);return(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg",children:(0,r.jsx)(P.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Activity History"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Comprehensive tracking sa lahat ng activities sa inyong tindahan - makita ang lahat ng nangyari"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mt-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-blue-600 dark:text-blue-400",children:"Total Activities"}),(0,r.jsx)("p",{className:"text-lg font-bold text-blue-900 dark:text-blue-300",children:ei.total})]}),(0,r.jsx)(j.A,{className:"h-5 w-5 text-blue-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-3 rounded-lg border border-green-200 dark:border-green-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-green-600 dark:text-green-400",children:"Today"}),(0,r.jsx)("p",{className:"text-lg font-bold text-green-900 dark:text-green-300",children:ei.todayCount})]}),(0,r.jsx)(W.A,{className:"h-5 w-5 text-green-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-yellow-600 dark:text-yellow-400",children:"This Week"}),(0,r.jsx)("p",{className:"text-lg font-bold text-yellow-900 dark:text-yellow-300",children:ei.weekCount})]}),(0,r.jsx)(M.A,{className:"h-5 w-5 text-yellow-500"})]})}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-3 rounded-lg border border-purple-200 dark:border-purple-800",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-xs font-medium text-purple-600 dark:text-purple-400",children:"Selected"}),(0,r.jsx)("p",{className:"text-lg font-bold text-purple-900 dark:text-purple-300",children:o.length})]}),(0,r.jsx)(eX.A,{className:"h-5 w-5 text-purple-500"})]})})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium ".concat("online"===z?"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400":"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"),children:["online"===z?(0,r.jsx)(e$.A,{className:"h-4 w-4"}):(0,r.jsx)(e0.A,{className:"h-4 w-4"}),"online"===z?"Online":"Offline"]}),(0,r.jsxs)("button",{onClick:()=>E(!L),className:"flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(L?"bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400":"bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"),children:[(0,r.jsx)(e1.A,{className:"h-4 w-4 ".concat(L?"animate-pulse":"")}),"Auto Refresh"]}),(0,r.jsxs)("button",{onClick:()=>y(!b),className:"btn-outline flex items-center gap-2 text-sm",disabled:b,children:[(0,r.jsx)(T.A,{className:"h-4 w-4 ".concat(b?"animate-spin":"")}),"Refresh"]}),o.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{onClick:()=>{el(Q.filter(e=>o.includes(e.id)))},className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all",children:[(0,r.jsx)(V.A,{className:"h-4 w-4"}),"Export Selected"]}),(0,r.jsxs)("button",{onClick:()=>{o.length>0&&x([])},className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all",children:[(0,r.jsx)(ee.A,{className:"h-4 w-4"}),"Delete Selected"]})]}),(0,r.jsxs)("button",{onClick:()=>el(),className:"btn-primary flex items-center gap-2 text-sm",children:[(0,r.jsx)(V.A,{className:"h-4 w-4"}),"Export All"]})]})]}),(0,r.jsxs)("div",{className:"card p-6 border-l-4 border-l-green-500",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-green-500"}),"Search & Filters"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("button",{onClick:()=>u(!m),className:"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white flex items-center gap-1 transition-colors",children:[m?(0,r.jsx)(e2.A,{className:"h-4 w-4"}):(0,r.jsx)(w.A,{className:"h-4 w-4"}),"Advanced Filters"]}),(0,r.jsx)("div",{className:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1",children:["list","timeline","grid"].map(e=>(0,r.jsx)("button",{onClick:()=>v(e),className:"px-3 py-1 text-xs font-medium rounded-md transition-all ".concat(f===e?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"),children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search activities, users, descriptions...",value:e,onChange:e=>a(e.target.value),className:"w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm"})]}),(0,r.jsxs)("select",{value:t,onChange:e=>s(e.target.value),className:"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm",children:[(0,r.jsx)("option",{value:"all",children:"All Types"}),(0,r.jsx)("option",{value:"product",children:"Product Activities"}),(0,r.jsx)("option",{value:"debt",children:"Debt Activities"}),(0,r.jsx)("option",{value:"payment",children:"Payment Activities"}),(0,r.jsx)("option",{value:"login",children:"Login Activities"}),(0,r.jsx)("option",{value:"system",children:"System Activities"}),(0,r.jsx)("option",{value:"security",children:"Security Activities"}),(0,r.jsx)("option",{value:"notification",children:"Notifications"})]}),(0,r.jsxs)("select",{value:i,onChange:e=>d(e.target.value),className:"px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm",children:[(0,r.jsx)("option",{value:"24hours",children:"Last 24 hours"}),(0,r.jsx)("option",{value:"7days",children:"Last 7 days"}),(0,r.jsx)("option",{value:"30days",children:"Last 30 days"}),(0,r.jsx)("option",{value:"90days",children:"Last 90 days"}),(0,r.jsx)("option",{value:"all",children:"All time"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("select",{value:k,onChange:e=>A(e.target.value),className:"flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 text-sm",children:[(0,r.jsx)("option",{value:"timestamp",children:"Sort by Time"}),(0,r.jsx)("option",{value:"type",children:"Sort by Type"}),(0,r.jsx)("option",{value:"priority",children:"Sort by Priority"})]}),(0,r.jsx)("button",{onClick:()=>D("asc"===C?"desc":"asc"),className:"px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",title:"Sort ".concat("asc"===C?"Descending":"Ascending"),children:"asc"===C?"↑":"↓"})]})]}),m&&(0,r.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4 animate-fade-in-up",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Priority Levels"}),(0,r.jsx)("div",{className:"space-y-2",children:X.map(e=>(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:U.priorities.includes(e),onChange:a=>{a.target.checked?O(a=>({...a,priorities:[...a.priorities,e]})):O(a=>({...a,priorities:a.priorities.filter(a=>a!==e)}))},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsxs)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize flex items-center gap-1",children:[Y(e),e]})]},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Status Types"}),(0,r.jsx)("div",{className:"space-y-2",children:$.map(e=>(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:U.statuses.includes(e),onChange:a=>{a.target.checked?O(a=>({...a,statuses:[...a.statuses,e]})):O(a=>({...a,statuses:a.statuses.filter(a=>a!==e)}))},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsx)("span",{className:"ml-2 px-2 py-1 rounded-full text-xs font-medium ".concat(K(e)),children:e})]},e))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Users"}),(0,r.jsx)("div",{className:"space-y-2",children:et.map(e=>(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:U.users.includes(e),onChange:a=>{a.target.checked?O(a=>({...a,users:[...a.users,e]})):O(a=>({...a,users:a.users.filter(a=>a!==e)}))},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsxs)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300 flex items-center gap-1",children:[(0,r.jsx)(g.A,{className:"h-3 w-3"}),e]})]},e))})]})]}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsxs)("button",{onClick:()=>{O({types:[],priorities:[],statuses:[],dateRange:"7days",users:[],categories:[]}),a(""),s("all"),d("7days")},className:"text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 flex items-center gap-1 transition-colors",children:[(0,r.jsx)(ea.A,{className:"h-4 w-4"}),"Clear All Filters"]})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-blue-500"}),"Activity Distribution"]}),(0,r.jsx)("div",{className:"space-y-3",children:Object.entries(ei.byType).map(e=>{let[a,t]=e,s=(t/ei.total*100).toFixed(1);return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"p-1.5 rounded-lg ".concat(G(a)),children:q(a)}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize",children:a})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-500",style:{width:"".concat(s,"%")}})}),(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900 dark:text-white w-8",children:t}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 w-10",children:[s,"%"]})]})]},a)})})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2",children:[(0,r.jsx)(eQ.A,{className:"h-5 w-5 text-orange-500"}),"Priority Analysis"]}),(0,r.jsx)("div",{className:"space-y-3",children:Object.entries(ei.byPriority).map(e=>{let[a,t]=e,s=(t/ei.total*100).toFixed(1);return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[Y(a),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize",children:a})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ".concat("critical"===a?"bg-red-500":"high"===a?"bg-orange-500":"medium"===a?"bg-yellow-500":"bg-blue-500"),style:{width:"".concat(s,"%")}})}),(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900 dark:text-white w-8",children:t}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 w-10",children:[s,"%"]})]})]},a)})})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 text-green-500"}),"User Activity"]}),(0,r.jsx)("div",{className:"space-y-3",children:Object.entries(ei.byUser).map(e=>{let[a,t]=e,s=(t/ei.total*100).toFixed(1);return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white text-xs font-bold",children:a.charAt(0).toUpperCase()})}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:a})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-500",style:{width:"".concat(s,"%")}})}),(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900 dark:text-white w-8",children:t}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 w-10",children:[s,"%"]})]})]},a)})})]})]}),(0,r.jsxs)("div",{className:"card overflow-hidden",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(e5.A,{className:"h-5 w-5 text-green-500"}),"Activity Timeline (",Q.length,")"]}),Q.length>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("button",{onClick:()=>{o.length===Q.length?x([]):x(Q.map(e=>e.id))},className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors",children:[o.length===Q.length?(0,r.jsx)(eX.A,{className:"h-4 w-4 text-green-500"}):(0,r.jsx)(e4.A,{className:"h-4 w-4"}),"Select All"]}),o.length>0&&(0,r.jsxs)("span",{className:"text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-2 py-1 rounded-full",children:[o.length," selected"]})]})]}),b&&(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,r.jsx)(T.A,{className:"h-4 w-4 animate-spin"}),"Loading..."]})]})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200 dark:divide-gray-700 max-h-[600px] overflow-y-auto main-content-scroll",children:Q.map((e,a)=>(0,r.jsx)("div",{className:"p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 group ".concat(o.includes(e.id)?"bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500":""),style:{animationDelay:"".concat(50*a,"ms")},children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("button",{onClick:()=>es(e.id),className:"mt-1 opacity-0 group-hover:opacity-100 transition-opacity",children:o.includes(e.id)?(0,r.jsx)(eX.A,{className:"h-4 w-4 text-green-500"}):(0,r.jsx)(e4.A,{className:"h-4 w-4 text-gray-400 hover:text-gray-600"})}),(0,r.jsx)("div",{className:"p-3 rounded-xl shadow-sm ".concat(G(e.type)," transition-all group-hover:scale-105"),children:q(e.type)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between gap-4 mb-2",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-gray-900 dark:text-white",children:e.action}),(0,r.jsxs)("span",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ".concat(J(e.priority)),children:[Y(e.priority),e.priority]}),(0,r.jsx)("span",{className:"inline-flex px-2 py-1 rounded-full text-xs font-medium ".concat(K(e.status)),children:e.status})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 leading-relaxed",children:e.description}),e.tags&&e.tags.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-2",children:e.tags.map(e=>(0,r.jsxs)("span",{className:"inline-flex px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-md",children:["#",e]},e))})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400 font-medium",children:er(e.timestamp)})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-700",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(g.A,{className:"h-3 w-3"}),e.user]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(N.A,{className:"h-3 w-3"}),e.category]}),e.ipAddress&&(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(eJ.A,{className:"h-3 w-3"}),e.ipAddress]})]}),e.details&&(0,r.jsxs)("button",{onClick:()=>p(e),className:"text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 font-medium flex items-center gap-1 transition-colors",children:[(0,r.jsx)(Z.A,{className:"h-3 w-3"}),"View Details"]})]})]})]})},e.id))}),0===Q.length&&(0,r.jsxs)("div",{className:"p-16 text-center",children:[(0,r.jsx)("div",{className:"bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(P.A,{className:"h-12 w-12 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-3",children:"Walang Activities na Nakita"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto",children:"Try i-adjust ang inyong search terms o filter criteria para makita ang activities na inyong gipangita."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,r.jsx)("button",{onClick:()=>{a(""),s("all"),O({types:[],priorities:[],statuses:[],dateRange:"7days",users:[],categories:[]})},className:"btn-outline",children:"Clear Filters"}),(0,r.jsxs)("button",{onClick:()=>y(!b),className:"btn-primary",children:[(0,r.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Refresh Data"]})]})]})]}),h&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 animate-fade-in",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-3 rounded-xl ".concat(G(h.type)),children:q(h.type)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Activity Details"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:h.action})]})]}),(0,r.jsx)("button",{onClick:()=>p(null),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,r.jsx)(ea.A,{className:"h-5 w-5 text-gray-500"})})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Activity Type"}),(0,r.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,r.jsxs)("span",{className:"inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ".concat(G(h.type)),children:[q(h.type),h.type]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Priority Level"}),(0,r.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,r.jsxs)("span",{className:"inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ".concat(J(h.priority)),children:[Y(h.priority),h.priority]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Status"}),(0,r.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,r.jsx)("span",{className:"inline-flex px-3 py-1 rounded-full text-sm font-medium ".concat(K(h.status)),children:h.status})})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"User"}),(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),h.user]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Category"}),(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),h.category]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Timestamp"}),(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white mt-1 flex items-center gap-1",children:[(0,r.jsx)(M.A,{className:"h-4 w-4"}),new Date(h.timestamp).toLocaleString("en-PH")]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Description"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:h.description})]}),h.tags&&h.tags.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Tags"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:h.tags.map(e=>(0,r.jsxs)("span",{className:"inline-flex px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 text-sm rounded-full",children:["#",e]},e))})]}),h.details&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Additional Details"}),(0,r.jsx)("div",{className:"mt-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:(0,r.jsx)("pre",{className:"text-sm text-gray-900 dark:text-white whitespace-pre-wrap font-mono",children:JSON.stringify(h.details,null,2)})})]}),(h.ipAddress||h.userAgent)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Security Information"}),(0,r.jsxs)("div",{className:"mt-2 space-y-2",children:[h.ipAddress&&(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(eJ.A,{className:"h-4 w-4 text-orange-500"}),(0,r.jsx)("span",{className:"font-medium",children:"IP Address:"}),h.ipAddress]}),h.userAgent&&(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white flex items-center gap-2",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsx)("span",{className:"font-medium",children:"User Agent:"}),(0,r.jsx)("span",{className:"truncate",children:h.userAgent})]})]})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50",children:(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)("button",{onClick:()=>p(null),className:"px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:"Close"}),(0,r.jsxs)("button",{onClick:()=>{el([h]),p(null)},className:"btn-primary flex items-center gap-2",children:[(0,r.jsx)(V.A,{className:"h-4 w-4"}),"Export This Activity"]})]})})]})}),(0,r.jsx)("div",{className:"card p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-t-2 border-t-green-500",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full animate-pulse ".concat(L?"bg-green-500":"bg-gray-400")}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:L?"Real-time Updates Active":"Real-time Updates Paused"}),b&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(T.A,{className:"h-4 w-4 animate-spin text-blue-600"}),(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400",children:"Updating..."})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(M.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["Last updated: ",_.toLocaleTimeString("en-PH")]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(P.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["Next update: ",L?"30s":"Manual"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(e3.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["Total: ",Q.length," activities"]})]})]})]})})]})}var e9=t(5937),e8=t(3127),e7=t(8883),ae=t(9420),aa=t(6767),at=t(4869),ar=t(4940),as=t(9803),al=t(8749),ai=t(5196),an=t(4738),ad=t(2417),ao=t(3500),ac=t(2970),ax=t(5880),am=t(4229),ag=t(408);function au(){let[e,a]=(0,l.useState)("store"),[t,s]=(0,l.useState)(!1),i=(0,l.useRef)(null),{settings:n,updateSettings:d,saveSettings:o,isLoading:c,hasUnsavedChanges:u}=(0,ag.t)(),h=[{id:"store",label:"Store Info",icon:e9.A},{id:"profile",label:"Profile",icon:g.A},{id:"notifications",label:"Notifications",icon:B.A},{id:"security",label:"Security",icon:eJ.A},{id:"appearance",label:"Appearance",icon:e8.A},{id:"backup",label:"Backup",icon:eK.A}],p=e=>d("store",e),b=e=>d("profile",e),y=e=>d("notifications",e),f=e=>d("security",e),j=e=>d("appearance",e),v=e=>d("backup",e),k=async()=>{try{await o(),alert("Settings saved successfully!")}catch(e){console.error("Error saving settings:",e),alert("Error saving settings. Please try again.")}},N=()=>{console.log("Exporting data..."),alert("Data export started. You will receive an email when ready.")},w=()=>{console.log("Importing data..."),alert("Please select a backup file to import.")},A=(e,a)=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){let e=new FileReader;e.onload=e=>{var t;let r=null==(t=e.target)?void 0:t.result;"logo"===a?p({branding:{...n.store.branding,logo:r}}):"avatar"===a&&b({avatar:r})},e.readAsDataURL(r)}},C=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Personal Information"]}),(0,r.jsxs)("div",{className:"flex items-start space-x-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-3",children:[(0,r.jsx)("div",{className:"w-24 h-24 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center bg-gray-50 dark:bg-slate-700 overflow-hidden",children:n.profile.avatar?(0,r.jsx)("img",{src:n.profile.avatar,alt:"Profile Avatar",className:"w-full h-full object-cover"}):(0,r.jsx)(g.A,{className:"h-12 w-12 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("button",{onClick:()=>{let e=document.createElement("input");e.type="file",e.accept="image/*",e.onchange=e=>A(e,"avatar"),e.click()},className:"btn-outline text-sm px-3 py-1",children:"Upload Photo"}),n.profile.avatar&&(0,r.jsx)("button",{onClick:()=>b({avatar:null}),className:"text-red-600 hover:text-red-700 text-sm",children:"Remove"})]})]}),(0,r.jsxs)("div",{className:"flex-1 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"First Name *"}),(0,r.jsx)("input",{type:"text",value:n.profile.firstName,onChange:e=>b({firstName:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter first name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Last Name *"}),(0,r.jsx)("input",{type:"text",value:n.profile.lastName,onChange:e=>b({lastName:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter last name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(e7.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"email",value:n.profile.email,onChange:e=>b({email:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(ae.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"tel",value:n.profile.phone,onChange:e=>b({phone:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"+63 ************"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Role"}),(0,r.jsxs)("select",{value:n.profile.role,onChange:e=>b({role:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"Store Owner",children:"Store Owner"}),(0,r.jsx)("option",{value:"Manager",children:"Manager"}),(0,r.jsx)("option",{value:"Cashier",children:"Cashier"}),(0,r.jsx)("option",{value:"Staff",children:"Staff"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Date of Birth"}),(0,r.jsx)("input",{type:"date",value:n.profile.dateOfBirth,onChange:e=>b({dateOfBirth:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Bio"}),(0,r.jsx)("textarea",{value:n.profile.bio,onChange:e=>b({bio:e.target.value}),rows:3,className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Tell us about yourself..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(eO.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("textarea",{value:n.profile.address,onChange:e=>b({address:e.target.value}),rows:2,className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter your address"})]})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(D.A,{className:"h-5 w-5 mr-2 text-red-600"}),"Emergency Contact"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Contact Name"}),(0,r.jsx)("input",{type:"text",value:n.profile.emergencyContact.name,onChange:e=>b({emergencyContact:{...n.profile.emergencyContact,name:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Emergency contact name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(ae.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"tel",value:n.profile.emergencyContact.phone,onChange:e=>b({emergencyContact:{...n.profile.emergencyContact,phone:e.target.value}}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"+63 ************"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Relationship"}),(0,r.jsxs)("select",{value:n.profile.emergencyContact.relationship,onChange:e=>b({emergencyContact:{...n.profile.emergencyContact,relationship:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"Family",children:"Family"}),(0,r.jsx)("option",{value:"Friend",children:"Friend"}),(0,r.jsx)("option",{value:"Colleague",children:"Colleague"}),(0,r.jsx)("option",{value:"Other",children:"Other"})]})]})]})]})]}),S=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(B.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Alert Preferences"]}),(0,r.jsx)("div",{className:"space-y-4",children:Object.entries(n.notifications).filter(e=>{let[a]=e;return!["channels","customRules","templates"].includes(a)}).map(e=>{let[a,t]=e;return(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:a.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["lowStock"===a&&"Get notified when products are running low","newDebt"===a&&"Alert when new customer debt is recorded","paymentReceived"===a&&"Notification for debt payments","dailyReport"===a&&"Daily business summary report","weeklyReport"===a&&"Weekly business analytics report","emailNotifications"===a&&"Receive notifications via email","smsNotifications"===a&&"Receive notifications via SMS","pushNotifications"===a&&"Receive push notifications in browser"]})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer ml-4",children:[(0,r.jsx)("input",{type:"checkbox",checked:t,onChange:e=>y({[a]:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"})]})]},a)})})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(e7.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Delivery Channels"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(e7.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"email",value:n.notifications.channels.email,onChange:e=>y({channels:{...n.notifications.channels,email:e.target.value}}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"<EMAIL>"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"SMS Number"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(aa.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"tel",value:n.notifications.channels.sms,onChange:e=>y({channels:{...n.notifications.channels,sms:e.target.value}}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"+63 ************"})]})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Webhook URL (Optional)"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(at.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"url",value:n.notifications.channels.webhook,onChange:e=>y({channels:{...n.notifications.channels,webhook:e.target.value}}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"https://your-webhook-url.com/notifications"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Send notifications to external systems via webhook"})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(R.A,{className:"h-5 w-5 mr-2 text-yellow-600"}),"Custom Rules"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[n.notifications.customRules.map(e=>(0,r.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.enabled?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"),children:e.enabled?"Active":"Inactive"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-700 p-1",children:(0,r.jsx)(ar.A,{className:"h-4 w-4"})}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:e.enabled,onChange:a=>{y({customRules:n.notifications.customRules.map(t=>t.id===e.id?{...t,enabled:a.target.checked}:t)})},className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"})]})]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Condition:"})," ",e.condition]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Action:"})," ",e.action]})]})]},e.id)),(0,r.jsxs)("button",{className:"w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-green-500 hover:text-green-600 transition-colors flex items-center justify-center space-x-2",children:[(0,r.jsx)(_.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Add Custom Rule"})]})]})]})]}),P=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(as.A,{className:"h-5 w-5 mr-2 text-red-600"}),"Password Management"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Current Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:t?"text":"password",value:n.security.currentPassword,onChange:e=>f({currentPassword:e.target.value}),className:"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter current password"}),(0,r.jsx)("button",{type:"button",onClick:()=>s(!t),className:"absolute right-3 top-3 text-gray-400 hover:text-gray-600",children:t?(0,r.jsx)(al.A,{className:"h-5 w-5"}):(0,r.jsx)(Z.A,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"New Password"}),(0,r.jsx)("input",{type:t?"text":"password",value:n.security.newPassword,onChange:e=>f({newPassword:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter new password"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Confirm New Password"}),(0,r.jsx)("input",{type:t?"text":"password",value:n.security.confirmPassword,onChange:e=>f({confirmPassword:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Confirm new password"})]})]}),(0,r.jsx)("button",{className:"btn-primary w-fit",children:"Update Password"})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(aa.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Two-Factor Authentication"]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Enable 2FA"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Add an extra layer of security to your account"})]}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:n.security.twoFactorAuth,onChange:e=>f({twoFactorAuth:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"})]})]}),n.security.twoFactorAuth&&(0,r.jsxs)("div",{className:"mt-4 p-4 border border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,r.jsx)(ai.A,{className:"h-5 w-5 text-green-600"}),(0,r.jsx)("span",{className:"font-medium text-green-800 dark:text-green-300",children:"2FA is enabled"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"btn-outline text-sm",children:"View Recovery Codes"}),(0,r.jsx)("button",{className:"btn-outline text-sm text-red-600 border-red-300 hover:bg-red-50",children:"Disable 2FA"})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(M.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Session Management"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Session Timeout (minutes)"}),(0,r.jsxs)("select",{value:n.security.sessionTimeout,onChange:e=>f({sessionTimeout:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"15",children:"15 minutes"}),(0,r.jsx)("option",{value:"30",children:"30 minutes"}),(0,r.jsx)("option",{value:"60",children:"1 hour"}),(0,r.jsx)("option",{value:"120",children:"2 hours"}),(0,r.jsx)("option",{value:"480",children:"8 hours"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Password Expiry (days)"}),(0,r.jsxs)("select",{value:n.security.passwordExpiry,onChange:e=>f({passwordExpiry:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"30",children:"30 days"}),(0,r.jsx)("option",{value:"60",children:"60 days"}),(0,r.jsx)("option",{value:"90",children:"90 days"}),(0,r.jsx)("option",{value:"180",children:"180 days"}),(0,r.jsx)("option",{value:"365",children:"1 year"}),(0,r.jsx)("option",{value:"0",children:"Never"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Max Login Attempts"}),(0,r.jsxs)("select",{value:n.security.loginAttempts,onChange:e=>f({loginAttempts:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"3",children:"3 attempts"}),(0,r.jsx)("option",{value:"5",children:"5 attempts"}),(0,r.jsx)("option",{value:"10",children:"10 attempts"}),(0,r.jsx)("option",{value:"0",children:"Unlimited"})]})]})]})]})]}),L=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(an.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Theme Selection"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[{id:"light",name:"Light",icon:x.A,preview:"bg-white border-gray-200"},{id:"dark",name:"Dark",icon:m.A,preview:"bg-slate-800 border-slate-600"},{id:"auto",name:"Auto",icon:an.A,preview:"bg-gradient-to-r from-white to-slate-800 border-gray-400"}].map(e=>{let a=e.icon;return(0,r.jsxs)("label",{className:"cursor-pointer",children:[(0,r.jsx)("input",{type:"radio",name:"theme",value:e.id,checked:n.appearance.theme===e.id,onChange:e=>j({theme:e.target.value}),className:"sr-only"}),(0,r.jsxs)("div",{className:"p-4 border-2 rounded-lg transition-all ".concat(n.appearance.theme===e.id?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:[(0,r.jsx)("div",{className:"w-full h-20 rounded-md mb-3 border ".concat(e.preview)}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(a,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"font-medium",children:e.name})]})]})]},e.id)})})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(e8.A,{className:"h-5 w-5 mr-2 text-pink-600"}),"Color Scheme"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Object.entries(n.appearance.colorScheme).map(e=>{let[a,t]=e;return(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 capitalize",children:[a," Color"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"color",value:t,onChange:e=>j({colorScheme:{...n.appearance.colorScheme,[a]:e.target.value}}),className:"w-12 h-12 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer"}),(0,r.jsx)("input",{type:"text",value:t,onChange:e=>j({colorScheme:{...n.appearance.colorScheme,[a]:e.target.value}}),className:"flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"#000000"})]})]},a)})}),(0,r.jsxs)("div",{className:"mt-6 flex space-x-3",children:[(0,r.jsx)("button",{className:"btn-outline",children:"Reset to Default"}),(0,r.jsx)("button",{className:"btn-outline",children:"Preview Changes"})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(ad.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Layout Preferences"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Sidebar Position"}),(0,r.jsxs)("select",{value:n.appearance.layout.sidebarPosition,onChange:e=>j({layout:{...n.appearance.layout,sidebarPosition:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"left",children:"Left"}),(0,r.jsx)("option",{value:"right",children:"Right"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"UI Density"}),(0,r.jsxs)("select",{value:n.appearance.layout.density,onChange:e=>j({layout:{...n.appearance.layout,density:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"compact",children:"Compact"}),(0,r.jsx)("option",{value:"comfortable",children:"Comfortable"}),(0,r.jsx)("option",{value:"spacious",children:"Spacious"})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:n.appearance.layout.showAnimations,onChange:e=>j({layout:{...n.appearance.layout,showAnimations:e.target.checked}}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Enable animations"})]}),(0,r.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:n.appearance.layout.compactMode,onChange:e=>j({layout:{...n.appearance.layout,compactMode:e.target.checked}}),className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Compact mode"})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(ao.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Typography"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Font Family"}),(0,r.jsxs)("select",{value:n.appearance.typography.fontFamily,onChange:e=>j({typography:{...n.appearance.typography,fontFamily:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"Inter",children:"Inter"}),(0,r.jsx)("option",{value:"Roboto",children:"Roboto"}),(0,r.jsx)("option",{value:"Open Sans",children:"Open Sans"}),(0,r.jsx)("option",{value:"Poppins",children:"Poppins"}),(0,r.jsx)("option",{value:"Lato",children:"Lato"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Font Size"}),(0,r.jsxs)("select",{value:n.appearance.typography.fontSize,onChange:e=>j({typography:{...n.appearance.typography,fontSize:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"small",children:"Small"}),(0,r.jsx)("option",{value:"medium",children:"Medium"}),(0,r.jsx)("option",{value:"large",children:"Large"}),(0,r.jsx)("option",{value:"extra-large",children:"Extra Large"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Font Weight"}),(0,r.jsxs)("select",{value:n.appearance.typography.fontWeight,onChange:e=>j({typography:{...n.appearance.typography,fontWeight:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"light",children:"Light"}),(0,r.jsx)("option",{value:"normal",children:"Normal"}),(0,r.jsx)("option",{value:"medium",children:"Medium"}),(0,r.jsx)("option",{value:"semibold",children:"Semibold"}),(0,r.jsx)("option",{value:"bold",children:"Bold"})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Preview"}),(0,r.jsx)("div",{className:"text-gray-700 dark:text-gray-300",style:{fontFamily:n.appearance.typography.fontFamily,fontSize:"small"===n.appearance.typography.fontSize?"14px":"medium"===n.appearance.typography.fontSize?"16px":"large"===n.appearance.typography.fontSize?"18px":"20px",fontWeight:n.appearance.typography.fontWeight},children:"The quick brown fox jumps over the lazy dog. This is how your text will appear with the selected typography settings."})]})]})]}),E=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(eK.A,{className:"h-5 w-5 mr-2 text-blue-600"}),"Backup Configuration"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Auto Backup"}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:n.backup.autoBackup,onChange:e=>v({autoBackup:e.target.checked}),className:"sr-only peer"}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"}),(0,r.jsx)("span",{className:"ml-3 text-sm text-gray-700 dark:text-gray-300",children:"Enable automatic backups"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Backup Frequency"}),(0,r.jsxs)("select",{value:n.backup.backupFrequency,onChange:e=>v({backupFrequency:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",disabled:!n.backup.autoBackup,children:[(0,r.jsx)("option",{value:"hourly",children:"Every Hour"}),(0,r.jsx)("option",{value:"daily",children:"Daily"}),(0,r.jsx)("option",{value:"weekly",children:"Weekly"}),(0,r.jsx)("option",{value:"monthly",children:"Monthly"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Retention Period (Days)"}),(0,r.jsx)("input",{type:"number",min:"1",max:"365",value:n.backup.retentionDays,onChange:e=>v({retentionDays:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Next Backup"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-slate-700 rounded-lg",children:(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:n.backup.autoBackup?"Tomorrow at 2:00 AM":"Manual backup only"})})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(ac.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Cloud Storage"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Storage Provider"}),(0,r.jsxs)("select",{value:n.backup.cloudStorage.provider,onChange:e=>v({cloudStorage:{...n.backup.cloudStorage,provider:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",children:[(0,r.jsx)("option",{value:"local",children:"Local Storage"}),(0,r.jsx)("option",{value:"aws",children:"Amazon S3"}),(0,r.jsx)("option",{value:"google",children:"Google Cloud"}),(0,r.jsx)("option",{value:"azure",children:"Azure Blob"}),(0,r.jsx)("option",{value:"dropbox",children:"Dropbox"})]})]}),"local"!==n.backup.cloudStorage.provider&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Bucket/Container Name"}),(0,r.jsx)("input",{type:"text",value:n.backup.cloudStorage.bucket,onChange:e=>v({cloudStorage:{...n.backup.cloudStorage,bucket:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"my-backup-bucket"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Access Key"}),(0,r.jsx)("input",{type:"text",value:n.backup.cloudStorage.accessKey,onChange:e=>v({cloudStorage:{...n.backup.cloudStorage,accessKey:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Your access key"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Secret Key"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:t?"text":"password",value:n.backup.cloudStorage.secretKey,onChange:e=>v({cloudStorage:{...n.backup.cloudStorage,secretKey:e.target.value}}),className:"w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Your secret key"}),(0,r.jsx)("button",{type:"button",onClick:()=>s(!t),className:"absolute right-3 top-3 text-gray-400 hover:text-gray-600",children:t?(0,r.jsx)(al.A,{className:"h-5 w-5"}):(0,r.jsx)(Z.A,{className:"h-5 w-5"})})]})]})]})]}),"local"!==n.backup.cloudStorage.provider&&(0,r.jsxs)("div",{className:"mt-4 flex space-x-3",children:[(0,r.jsx)("button",{className:"btn-outline",children:"Test Connection"}),(0,r.jsx)("button",{className:"btn-primary",children:"Save Configuration"})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(eZ.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Backup History"]}),(0,r.jsx)("div",{className:"space-y-3",children:n.backup.backupHistory.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("completed"===e.status?"bg-green-500":"failed"===e.status?"bg-red-500":"bg-yellow-500")}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[e.type.charAt(0).toUpperCase()+e.type.slice(1)," Backup"]}),(0,r.jsxs)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:[new Date(e.timestamp).toLocaleString()," • ",e.size]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-700 p-1",title:"Download",children:(0,r.jsx)(V.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"text-green-600 hover:text-green-700 p-1",title:"Restore",children:(0,r.jsx)(T.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{className:"text-red-600 hover:text-red-700 p-1",title:"Delete",children:(0,r.jsx)(ee.A,{className:"h-4 w-4"})})]})]},e.id))}),(0,r.jsxs)("div",{className:"mt-4 flex justify-between items-center",children:[(0,r.jsx)("button",{className:"text-sm text-blue-600 hover:text-blue-700",children:"View All Backups"}),(0,r.jsx)("button",{className:"text-sm text-red-600 hover:text-red-700",children:"Clear Old Backups"})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(ax.A,{className:"h-5 w-5 mr-2 text-indigo-600"}),"Manual Operations"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("button",{onClick:N,className:"flex items-center justify-center px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors",children:[(0,r.jsx)(V.A,{className:"h-5 w-5 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:"Create Backup"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Export all data now"})]})]}),(0,r.jsxs)("button",{onClick:w,className:"flex items-center justify-center px-6 py-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors",children:[(0,r.jsx)(et.A,{className:"h-5 w-5 mr-3"}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:"Restore Data"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Import from backup"})]})]})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(D.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-yellow-800 dark:text-yellow-300",children:"Important"}),(0,r.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-400 mt-1",children:"Always verify your backups before relying on them. Test restore procedures regularly to ensure data integrity."})]})]})}),(0,r.jsx)("div",{className:"mt-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Last Backup"}),(0,r.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:new Date(n.backup.lastBackup).toLocaleString()})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Status"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(ai.A,{className:"h-4 w-4 text-green-600 mr-1"}),(0,r.jsx)("span",{className:"text-xs text-green-600",children:"Completed"})]})]})]})})]})]}),I=()=>(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(e9.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Basic Information"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Store Name *"}),(0,r.jsx)("input",{type:"text",value:n.store.name,onChange:e=>p({name:e.target.value}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter store name"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Website"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(at.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"url",value:n.store.website,onChange:e=>p({website:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"https://yourstore.com"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(ae.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"tel",value:n.store.phone,onChange:e=>p({phone:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"+63 ************"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(e7.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"email",value:n.store.email,onChange:e=>p({email:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"<EMAIL>"})]})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Address *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(eO.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsx)("textarea",{value:n.store.address,onChange:e=>p({address:e.target.value}),rows:3,className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Enter complete store address"})]})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(M.A,{className:"h-5 w-5 mr-2 text-purple-600"}),"Business Hours & Operating Days"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Opening Time"}),(0,r.jsx)("input",{type:"time",value:n.store.businessHours.open,onChange:e=>p({businessHours:{...n.store.businessHours,open:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Closing Time"}),(0,r.jsx)("input",{type:"time",value:n.store.businessHours.close,onChange:e=>p({businessHours:{...n.store.businessHours,close:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"Operating Days"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3",children:["monday","tuesday","wednesday","thursday","friday","saturday","sunday"].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:n.store.operatingDays.includes(e),onChange:a=>{p({operatingDays:a.target.checked?[...n.store.operatingDays,e]:n.store.operatingDays.filter(a=>a!==e)})},className:"rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300 capitalize",children:e.slice(0,3)})]},e))})]})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)(e8.A,{className:"h-5 w-5 mr-2 text-pink-600"}),"Store Branding"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Store Logo"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-20 h-20 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center bg-gray-50 dark:bg-slate-700",children:n.store.branding.logo?(0,r.jsx)("img",{src:n.store.branding.logo,alt:"Store Logo",className:"w-full h-full object-cover rounded-lg"}):(0,r.jsx)(eL.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("button",{onClick:()=>{var e;return null==(e=i.current)?void 0:e.click()},className:"btn-outline text-sm px-4 py-2",children:"Upload Logo"}),n.store.branding.logo&&(0,r.jsx)("button",{onClick:()=>p({branding:{...n.store.branding,logo:null}}),className:"text-red-600 hover:text-red-700 text-sm",children:"Remove"})]})]}),(0,r.jsx)("input",{ref:i,type:"file",accept:"image/*",onChange:e=>A(e,"logo"),className:"hidden"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Store Slogan"}),(0,r.jsx)("input",{type:"text",value:n.store.branding.slogan,onChange:e=>p({branding:{...n.store.branding,slogan:e.target.value}}),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700 transition-all",placeholder:"Your Neighborhood Store"})]})]})]})]});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Settings"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[u&&(0,r.jsxs)("span",{className:"text-sm text-yellow-600 dark:text-yellow-400 flex items-center",children:[(0,r.jsx)(D.A,{className:"h-4 w-4 mr-1"}),"Unsaved changes"]}),(0,r.jsxs)("button",{onClick:k,disabled:c||!u,className:"btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[c?(0,r.jsx)(T.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,r.jsx)(am.A,{className:"h-4 w-4 mr-2"}),c?"Saving...":"Save Changes"]})]})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,r.jsx)("nav",{className:"flex space-x-8 px-6",children:h.map(t=>{let s=t.icon;return(0,r.jsxs)("button",{onClick:()=>a(t.id),className:"flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat(e===t.id?"border-green-500 text-green-600 dark:text-green-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,r.jsx)(s,{className:"h-5 w-5 mr-2"}),t.label]},t.id)})})}),(0,r.jsx)("div",{className:"p-6",children:(()=>{switch(e){case"store":return I();case"profile":return C();case"notifications":return S();case"security":return P();case"appearance":return L();case"backup":return E();default:return(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("p",{className:"text-gray-500 dark:text-gray-400",children:[e.charAt(0).toUpperCase()+e.slice(1)," settings coming soon..."]})})}})()})]})]})}var ah=t(9376),ap=t(3311),ab=t(5273),ay=t(2152),af=t(133),aj=t(1981),av=t(4311),ak=t(4357),aN=t(1154),aw=t(2486),aA=t(6408),aC=t(760);function aS(e){let{context:a="dashboard"}=e,{resolvedTheme:t}=(0,s.D)(),[i,n]=(0,l.useState)(!1),[d,o]=(0,l.useState)(!1),[c,x]=(0,l.useState)([{id:"1",type:"ai",content:"Hello! I'm your AI assistant for Revantad Store. I can help you with business analytics, inventory management, customer debt tracking, and store operations. How can I assist you today?",timestamp:new Date}]),[m,u]=(0,l.useState)(""),[h,p]=(0,l.useState)(!1),[b,y]=(0,l.useState)(!0),[j,v]=(0,l.useState)(null),[k,N]=(0,l.useState)(!1),w=(0,l.useRef)(null),A=(0,l.useRef)(null),C=()=>{var e;null==(e=w.current)||e.scrollIntoView({behavior:"smooth"})};(0,l.useEffect)(()=>{C()},[c]),(0,l.useEffect)(()=>{i&&!d&&A.current&&A.current.focus()},[i,d]);let S=async()=>{if(!m.trim()||h)return;let e={id:Date.now().toString(),type:"user",content:m.trim(),timestamp:new Date};x(a=>[...a,e]),u(""),p(!0),N(!0);let t={id:"typing",type:"ai",content:"",timestamp:new Date,isTyping:!0};x(e=>[...e,t]);try{let t=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e.content,context:a})}),r=await t.json();if(x(e=>e.filter(e=>"typing"!==e.id)),r.success){let e={id:(Date.now()+1).toString(),type:"ai",content:r.response,timestamp:new Date};x(a=>[...a,e]),b&&D()}else throw Error(r.error||"Failed to get AI response")}catch(a){x(e=>e.filter(e=>"typing"!==e.id));let e={id:(Date.now()+1).toString(),type:"ai",content:"Sorry, I encountered an error. Please try again later.",timestamp:new Date};x(a=>[...a,e])}finally{p(!1),N(!1)}},D=()=>{let e=new(window.AudioContext||window.webkitAudioContext),a=e.createOscillator(),t=e.createGain();a.connect(t),t.connect(e.destination),a.frequency.setValueAtTime(800,e.currentTime),a.frequency.setValueAtTime(600,e.currentTime+.1),t.gain.setValueAtTime(0,e.currentTime),t.gain.linearRampToValueAtTime(.1,e.currentTime+.01),t.gain.exponentialRampToValueAtTime(.01,e.currentTime+.2),a.start(e.currentTime),a.stop(e.currentTime+.2)},P=async(e,a)=>{try{await navigator.clipboard.writeText(e),v(a),setTimeout(()=>v(null),2e3)}catch(e){console.error("Failed to copy message:",e)}},M=e=>e.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(aA.P.div,{className:"fixed bottom-6 right-6 z-50 ".concat(i?"hidden":"block"),initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{type:"spring",stiffness:260,damping:20,delay:.2},children:[(0,r.jsxs)(aA.P.button,{onClick:()=>n(!0),className:"relative p-4 rounded-full shadow-2xl transition-all duration-300 focus:outline-none focus:ring-4 group overflow-hidden",style:{background:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",boxShadow:"dark"===t?"0 20px 40px rgba(139, 92, 246, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1)":"0 20px 40px rgba(139, 92, 246, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.05)",focusRingColor:"#8b5cf6"},whileHover:{scale:1.1,boxShadow:"dark"===t?"0 25px 50px rgba(139, 92, 246, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.2)":"0 25px 50px rgba(139, 92, 246, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.1)"},whileTap:{scale:.95},children:[(0,r.jsx)("div",{className:"absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300",style:{background:"linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%)"}}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-full",children:(0,r.jsx)("div",{className:"absolute inset-0 rounded-full animate-ping opacity-20",style:{background:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)"}})}),(0,r.jsxs)("div",{className:"relative z-10 flex items-center justify-center",children:[(0,r.jsx)(aA.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0,repeatDelay:3},children:(0,r.jsx)(ah.A,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-green-400 rounded-full animate-ping opacity-75"})}),(0,r.jsx)(aA.P.div,{className:"absolute -top-2 -left-2",animate:{scale:[0,1,0],rotate:[0,180,360]},transition:{duration:2,repeat:1/0,repeatDelay:1,delay:.5},children:(0,r.jsx)(ap.A,{className:"w-3 h-3 text-yellow-300"})}),(0,r.jsx)(aA.P.div,{className:"absolute -bottom-2 -right-2",animate:{scale:[0,1,0],rotate:[360,180,0]},transition:{duration:2,repeat:1/0,repeatDelay:1,delay:1.5},children:(0,r.jsx)(R.A,{className:"w-3 h-3 text-yellow-300"})})]})]}),(0,r.jsxs)(aA.P.div,{className:"absolute right-full mr-3 top-1/2 transform -translate-y-1/2 px-3 py-2 rounded-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap",style:{backgroundColor:"dark"===t?"#1e293b":"#ffffff",color:"dark"===t?"#f8fafc":"#111827",border:"dark"===t?"1px solid rgba(148, 163, 184, 0.3)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===t?"0 4px 12px rgba(0, 0, 0, 0.3)":"0 4px 12px rgba(0, 0, 0, 0.15)"},initial:{opacity:0,x:10},whileHover:{opacity:1,x:0},children:["Ask AI Assistant",(0,r.jsx)("div",{className:"absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0",style:{borderTop:"6px solid transparent",borderBottom:"6px solid transparent",borderLeft:"6px solid ".concat("dark"===t?"#1e293b":"#ffffff")}})]})]}),(0,r.jsx)(aC.N,{children:i&&(0,r.jsxs)(aA.P.div,{className:"fixed bottom-6 right-6 z-50 rounded-2xl shadow-2xl overflow-hidden transition-all duration-300 ".concat(d?"w-80 h-16":"w-96 h-[600px]"),style:{backgroundColor:"dark"===t?"#1e293b":"#ffffff",border:"dark"===t?"1px solid rgba(148, 163, 184, 0.3)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===t?"0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)":"0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)"},initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},exit:{scale:0,opacity:0},transition:{type:"spring",stiffness:260,damping:20},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",style:{background:"dark"===t?"linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(124, 58, 237, 0.15) 100%)":"linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.08) 100%)",borderColor:"dark"===t?"rgba(148, 163, 184, 0.2)":"rgba(229, 231, 235, 0.8)"},children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg",style:{background:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",boxShadow:"0 4px 8px rgba(139, 92, 246, 0.3)"},children:(0,r.jsx)(f.A,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-sm",style:{color:"dark"===t?"#f8fafc":"#111827"},children:"AI Assistant"}),(0,r.jsx)("p",{className:"text-xs",style:{color:"dark"===t?"#94a3b8":"#64748b"},children:"Revantad Store Helper"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("button",{onClick:()=>y(!b),className:"p-1.5 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-105",title:b?"Disable sound":"Enable sound",children:b?(0,r.jsx)(ab.A,{className:"w-4 h-4",style:{color:"dark"===t?"#94a3b8":"#64748b"}}):(0,r.jsx)(ay.A,{className:"w-4 h-4",style:{color:"dark"===t?"#94a3b8":"#64748b"}})}),(0,r.jsx)("button",{onClick:()=>{x([{id:"1",type:"ai",content:"Hello! I'm your AI assistant for Revantad Store. I can help you with business analytics, inventory management, customer debt tracking, and store operations. How can I assist you today?",timestamp:new Date}])},className:"p-1.5 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-105",title:"Clear chat",children:(0,r.jsx)(af.A,{className:"w-4 h-4",style:{color:"dark"===t?"#94a3b8":"#64748b"}})}),(0,r.jsx)("button",{onClick:()=>o(!d),className:"p-1.5 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-105",title:d?"Maximize":"Minimize",children:d?(0,r.jsx)(aj.A,{className:"w-4 h-4",style:{color:"dark"===t?"#94a3b8":"#64748b"}}):(0,r.jsx)(av.A,{className:"w-4 h-4",style:{color:"dark"===t?"#94a3b8":"#64748b"}})}),(0,r.jsx)("button",{onClick:()=>n(!1),className:"p-1.5 rounded-lg transition-all duration-200 hover:bg-red-100 dark:hover:bg-red-900/20 hover:scale-105",title:"Close chat",children:(0,r.jsx)(ea.A,{className:"w-4 h-4 hover:text-red-500",style:{color:"dark"===t?"#94a3b8":"#64748b"}})})]})]}),!d&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4 h-[480px] chat-scroll",children:[c.map(e=>e.isTyping?(0,r.jsx)(aA.P.div,{className:"flex justify-start",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("div",{className:"p-2 rounded-full",style:{backgroundColor:"dark"===t?"#8b5cf6":"#7c3aed"},children:(0,r.jsx)(f.A,{className:"w-4 h-4 text-white"})}),(0,r.jsxs)("div",{className:"p-3 rounded-2xl rounded-tl-md flex items-center space-x-2",style:{backgroundColor:"dark"===t?"rgba(71, 85, 105, 0.3)":"rgba(243, 244, 246, 0.8)"},children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)(aA.P.div,{className:"w-2 h-2 rounded-full",style:{backgroundColor:"dark"===t?"#8b5cf6":"#7c3aed"},animate:{scale:[1,1.2,1]},transition:{duration:.6,repeat:1/0,delay:0}}),(0,r.jsx)(aA.P.div,{className:"w-2 h-2 rounded-full",style:{backgroundColor:"dark"===t?"#8b5cf6":"#7c3aed"},animate:{scale:[1,1.2,1]},transition:{duration:.6,repeat:1/0,delay:.2}}),(0,r.jsx)(aA.P.div,{className:"w-2 h-2 rounded-full",style:{backgroundColor:"dark"===t?"#8b5cf6":"#7c3aed"},animate:{scale:[1,1.2,1]},transition:{duration:.6,repeat:1/0,delay:.4}})]}),(0,r.jsx)("span",{className:"text-sm",style:{color:"dark"===t?"#f8fafc":"#111827"},children:"AI is thinking..."})]})]})},e.id):(0,r.jsx)(aA.P.div,{className:"flex ".concat("user"===e.type?"justify-end":"justify-start"," group"),initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},transition:{duration:.3,type:"spring",stiffness:200},children:(0,r.jsxs)("div",{className:"flex items-start space-x-2 max-w-[80%] ".concat("user"===e.type?"flex-row-reverse space-x-reverse":""),children:[(0,r.jsxs)(aA.P.div,{className:"p-2 rounded-full flex-shrink-0 relative overflow-hidden",style:{backgroundColor:"user"===e.type?"dark"===t?"#22c55e":"#16a34a":"dark"===t?"#8b5cf6":"#7c3aed"},whileHover:{scale:1.1},children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-300",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)",transform:"translateX(-100%)",animation:"shimmer 1.5s infinite"}}),"user"===e.type?(0,r.jsx)(g.A,{className:"w-4 h-4 text-white relative z-10"}):(0,r.jsx)(f.A,{className:"w-4 h-4 text-white relative z-10"})]}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsxs)(aA.P.div,{className:"p-3 rounded-2xl ".concat("user"===e.type?"rounded-tr-md":"rounded-tl-md"," relative overflow-hidden"),style:{backgroundColor:"user"===e.type?"dark"===t?"rgba(34, 197, 94, 0.2)":"rgba(34, 197, 94, 0.1)":"dark"===t?"rgba(71, 85, 105, 0.3)":"rgba(243, 244, 246, 0.8)",color:"dark"===t?"#f8fafc":"#111827",border:"1px solid ".concat("user"===e.type?"dark"===t?"rgba(34, 197, 94, 0.3)":"rgba(34, 197, 94, 0.2)":"dark"===t?"rgba(139, 92, 246, 0.3)":"rgba(139, 92, 246, 0.2)")},whileHover:{scale:1.02},children:[(0,r.jsx)("p",{className:"text-sm whitespace-pre-wrap leading-relaxed",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("p",{className:"text-xs opacity-70",style:{color:"dark"===t?"#94a3b8":"#64748b"},children:M(e.timestamp)}),"ai"===e.type&&(0,r.jsx)("button",{onClick:()=>P(e.content,e.id),className:"opacity-0 group-hover:opacity-100 p-1 rounded transition-all duration-200 hover:bg-gray-200 dark:hover:bg-gray-600",title:"Copy message",children:j===e.id?(0,r.jsx)(ai.A,{className:"w-3 h-3 text-green-500"}):(0,r.jsx)(ak.A,{className:"w-3 h-3",style:{color:"dark"===t?"#94a3b8":"#64748b"}})})]})]})})]})},e.id)),(0,r.jsx)("div",{ref:w})]}),(0,r.jsxs)("div",{className:"p-4 border-t backdrop-blur-sm",style:{borderColor:"dark"===t?"rgba(148, 163, 184, 0.2)":"rgba(229, 231, 235, 0.8)",background:"dark"===t?"linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)":"linear-gradient(135deg, rgba(249, 250, 251, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%)"},children:[(0,r.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)("textarea",{ref:A,value:m,onChange:e=>u(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),S())},placeholder:"Ask me anything about your store...",disabled:h,rows:1,className:"w-full p-3 pr-12 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none",style:{backgroundColor:"dark"===t?"#334155":"#ffffff",borderColor:"dark"===t?"rgba(148, 163, 184, 0.3)":"rgba(229, 231, 235, 0.8)",color:"dark"===t?"#f8fafc":"#111827",minHeight:"44px",maxHeight:"120px"},onInput:e=>{let a=e.target;a.style.height="auto",a.style.height=Math.min(a.scrollHeight,120)+"px"}}),(0,r.jsxs)("div",{className:"absolute bottom-2 right-2 text-xs opacity-50",style:{color:"dark"===t?"#94a3b8":"#64748b"},children:[m.length,"/500"]})]}),(0,r.jsxs)(aA.P.button,{onClick:S,disabled:!m.trim()||h,className:"p-3 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500 relative overflow-hidden",style:{background:!m.trim()||h?"dark"===t?"rgba(71, 85, 105, 0.5)":"rgba(156, 163, 175, 0.5)":"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",boxShadow:!m.trim()||h?"none":"0 4px 8px rgba(139, 92, 246, 0.3)"},whileHover:!m.trim()||h?{}:{scale:1.05},whileTap:!m.trim()||h?{}:{scale:.95},children:[m.trim()&&!h&&(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl",children:(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl animate-pulse opacity-20",style:{background:"linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%)"}})}),(0,r.jsx)("div",{className:"relative z-10",children:h?(0,r.jsx)(aN.A,{className:"w-4 h-4 text-white animate-spin"}):(0,r.jsx)(aw.A,{className:"w-4 h-4 text-white"})})]})]}),!m&&(0,r.jsx)(aA.P.div,{className:"mt-2 flex flex-wrap gap-1",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:["Sales help","Inventory tips","Customer advice"].map((e,a)=>(0,r.jsx)(aA.P.button,{onClick:()=>u(e),className:"px-2 py-1 text-xs rounded-full border transition-all duration-200 hover:scale-105",style:{backgroundColor:"dark"===t?"rgba(139, 92, 246, 0.1)":"rgba(139, 92, 246, 0.05)",borderColor:"dark"===t?"rgba(139, 92, 246, 0.3)":"rgba(139, 92, 246, 0.2)",color:"dark"===t?"#a855f7":"#7c3aed"},whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:.3+.1*a},children:e},e))})]})]})]})})]})}var aD=t(463),aP=t(9947);let aM=[{icon:W.A,title:"Sales Analysis",prompt:"Analyze my current sales performance and suggest improvements"},{icon:n.A,title:"Inventory Management",prompt:"Help me optimize my inventory levels and identify slow-moving products"},{icon:I.A,title:"Debt Management",prompt:"Provide strategies for managing customer debts effectively"},{icon:j.A,title:"Business Insights",prompt:"Give me insights on how to grow my sari-sari store business"},{icon:aD.A,title:"Marketing Ideas",prompt:"Suggest marketing strategies to attract more customers"},{icon:aP.A,title:"General Help",prompt:"What can you help me with regarding my store management?"}];function aT(){let{resolvedTheme:e}=(0,s.D)(),[a,t]=(0,l.useState)([{id:"1",type:"ai",content:"Welcome to AI Support! I'm here to help you manage your Revantad Store more effectively. I can assist with business analytics, inventory management, customer relationships, financial planning, and much more. How can I help you today?",timestamp:new Date}]),[i,n]=(0,l.useState)(""),[d,o]=(0,l.useState)(!1),c=(0,l.useRef)(null),x=(0,l.useRef)(null),m=()=>{var e;null==(e=c.current)||e.scrollIntoView({behavior:"smooth"})};(0,l.useEffect)(()=>{m()},[a]),(0,l.useEffect)(()=>{x.current&&x.current.focus()},[]);let u=async e=>{let a=e||i.trim();if(!a||d)return;let r={id:Date.now().toString(),type:"user",content:a,timestamp:new Date};t(e=>[...e,r]),n(""),o(!0);try{let e=await fetch("/api/ai",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:a,context:"ai-support"})}),r=await e.json();if(r.success){let e={id:(Date.now()+1).toString(),type:"ai",content:r.response,timestamp:new Date};t(a=>[...a,e])}else throw Error(r.error||"Failed to get AI response")}catch(a){let e={id:(Date.now()+1).toString(),type:"ai",content:"Sorry, I encountered an error. Please try again later.",timestamp:new Date};t(a=>[...a,e])}finally{o(!1)}},h=e=>e.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0});return(0,r.jsxs)("div",{className:"h-full flex flex-col",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(aA.P.div,{className:"p-3 rounded-xl mr-3",style:{background:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",boxShadow:"0 4px 8px rgba(139, 92, 246, 0.3)"},whileHover:{scale:1.05},children:(0,r.jsx)(ah.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold",style:{color:"dark"===e?"#f8fafc":"#111827"},children:"AI Business Assistant"}),(0,r.jsx)("p",{className:"text-sm opacity-80",style:{color:"dark"===e?"#cbd5e1":"#64748b"},children:"Intelligent support for your sari-sari store operations"})]})]})}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"dark"===e?"#f8fafc":"#111827"},children:[(0,r.jsx)(R.A,{className:"w-5 h-5 mr-2 text-purple-500"}),"Quick Actions"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:aM.map((a,t)=>{let s=a.icon;return(0,r.jsxs)(aA.P.button,{onClick:()=>u(a.prompt),className:"p-5 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 text-left",style:{backgroundColor:"dark"===e?"rgba(71, 85, 105, 0.3)":"rgba(243, 244, 246, 0.8)",borderColor:"dark"===e?"rgba(148, 163, 184, 0.3)":"rgba(229, 231, 235, 0.8)",boxShadow:"dark"===e?"0 4px 6px rgba(0, 0, 0, 0.1)":"0 4px 6px rgba(0, 0, 0, 0.05)"},whileHover:{scale:1.03,boxShadow:"dark"===e?"0 8px 25px rgba(139, 92, 246, 0.2)":"0 8px 25px rgba(139, 92, 246, 0.15)"},whileTap:{scale:.98},disabled:d,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},children:[(0,r.jsxs)("div",{className:"flex items-center mb-3",children:[(0,r.jsx)(aA.P.div,{className:"p-2 rounded-lg mr-3",style:{backgroundColor:"dark"===e?"rgba(139, 92, 246, 0.2)":"rgba(139, 92, 246, 0.1)",border:"1px solid ".concat("dark"===e?"rgba(139, 92, 246, 0.3)":"rgba(139, 92, 246, 0.2)")},whileHover:{scale:1.1,rotate:5},children:(0,r.jsx)(s,{className:"w-5 h-5 text-purple-500"})}),(0,r.jsx)("span",{className:"font-semibold text-sm",style:{color:"dark"===e?"#f8fafc":"#111827"},children:a.title})]}),(0,r.jsx)("p",{className:"text-xs leading-relaxed",style:{color:"dark"===e?"#cbd5e1":"#64748b"},children:a.prompt})]},t)})})]}),(0,r.jsx)("div",{className:"flex-1 flex flex-col",children:(0,r.jsxs)("div",{className:"flex-1 rounded-xl border p-4 mb-4 overflow-hidden",style:{backgroundColor:"dark"===e?"rgba(30, 41, 59, 0.5)":"rgba(255, 255, 255, 0.8)",borderColor:"dark"===e?"rgba(148, 163, 184, 0.3)":"rgba(229, 231, 235, 0.8)",boxShadow:"dark"===e?"0 4px 6px rgba(0, 0, 0, 0.1)":"0 4px 6px rgba(0, 0, 0, 0.05)"},children:[(0,r.jsxs)("div",{className:"h-96 overflow-y-auto space-y-4 mb-4",children:[a.map(a=>(0,r.jsx)("div",{className:"flex ".concat("user"===a.type?"justify-end":"justify-start"),children:(0,r.jsxs)("div",{className:"flex items-start space-x-3 max-w-[80%] ".concat("user"===a.type?"flex-row-reverse space-x-reverse":""),children:[(0,r.jsx)("div",{className:"p-2 rounded-full flex-shrink-0",style:{backgroundColor:"user"===a.type?"dark"===e?"#22c55e":"#16a34a":"dark"===e?"#8b5cf6":"#7c3aed"},children:"user"===a.type?(0,r.jsx)(g.A,{className:"w-4 h-4 text-white"}):(0,r.jsx)(f.A,{className:"w-4 h-4 text-white"})}),(0,r.jsxs)("div",{className:"p-4 rounded-2xl ".concat("user"===a.type?"rounded-tr-md":"rounded-tl-md"),style:{backgroundColor:"user"===a.type?"dark"===e?"rgba(34, 197, 94, 0.2)":"rgba(34, 197, 94, 0.1)":"dark"===e?"rgba(71, 85, 105, 0.4)":"rgba(243, 244, 246, 0.9)",color:"dark"===e?"#f8fafc":"#111827"},children:[(0,r.jsx)("p",{className:"text-sm whitespace-pre-wrap leading-relaxed",children:a.content}),(0,r.jsx)("p",{className:"text-xs mt-2 opacity-70",style:{color:"dark"===e?"#94a3b8":"#64748b"},children:h(a.timestamp)})]})]})},a.id)),d&&(0,r.jsx)("div",{className:"flex justify-start",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-full",style:{backgroundColor:"dark"===e?"#8b5cf6":"#7c3aed"},children:(0,r.jsx)(f.A,{className:"w-4 h-4 text-white"})}),(0,r.jsxs)("div",{className:"p-4 rounded-2xl rounded-tl-md flex items-center space-x-3",style:{backgroundColor:"dark"===e?"rgba(71, 85, 105, 0.4)":"rgba(243, 244, 246, 0.9)"},children:[(0,r.jsx)(aN.A,{className:"w-4 h-4 animate-spin",style:{color:"dark"===e?"#8b5cf6":"#7c3aed"}}),(0,r.jsx)("span",{className:"text-sm",style:{color:"dark"===e?"#f8fafc":"#111827"},children:"Analyzing your request..."})]})]})}),(0,r.jsx)("div",{ref:c})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{ref:x,type:"text",value:i,onChange:e=>n(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),u())},placeholder:"Ask me anything about your store management...",disabled:d,className:"flex-1 p-4 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500",style:{backgroundColor:"dark"===e?"#334155":"#ffffff",borderColor:"dark"===e?"rgba(148, 163, 184, 0.3)":"rgba(229, 231, 235, 0.8)",color:"dark"===e?"#f8fafc":"#111827"}}),(0,r.jsx)("button",{onClick:()=>u(),disabled:!i.trim()||d,className:"p-4 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500",style:{background:"linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",boxShadow:"0 4px 8px rgba(139, 92, 246, 0.3)"},children:(0,r.jsx)(aw.A,{className:"w-5 h-5 text-white"})})]})]})})]})}var aL=t(5695);function aE(e){let{children:a}=e,{isAuthenticated:t,isLoading:s}=(0,b.A)(),i=(0,aL.useRouter)();return((0,l.useEffect)(()=>{s||t||i.push("/login")},[t,s,i]),s)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:"R"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Loading Revantad Store"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Please wait while we prepare your dashboard..."})]})}):t?(0,r.jsx)(r.Fragment,{children:a}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("span",{className:"text-red-600 dark:text-red-400 font-bold text-2xl",children:"!"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Redirecting to login page..."})]})})}function aR(){let[e,a]=(0,l.useState)("dashboard"),{resolvedTheme:t}=(0,s.D)(),[i,n]=(0,l.useState)({totalProducts:0,totalDebts:0,totalDebtAmount:0,lowStockItems:0,recentProducts:[],recentDebts:[]});(0,l.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/products"),a=(await e.json()).products||[],t=await fetch("/api/debts"),r=(await t.json()).debts||[],s=r.reduce((e,a)=>e+a.total_amount,0),l=a.filter(e=>e.stock_quantity<10).length;n({totalProducts:a.length,totalDebts:r.length,totalDebtAmount:s,lowStockItems:l,recentProducts:a.slice(0,5),recentDebts:r.slice(0,5)})}catch(e){console.error("Error fetching stats:",e)}};return(0,r.jsx)(aE,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300",style:{backgroundColor:"dark"===t?"#0f172a":"#f9fafb"},children:[(0,r.jsx)(y,{activeSection:e,setActiveSection:a}),(0,r.jsxs)("div",{className:"flex pt-16",children:[(0,r.jsx)(C,{activeSection:e,setActiveSection:a}),(0,r.jsx)("main",{className:"flex-1 transition-colors duration-300 main-content-scroll",style:{backgroundColor:"dark"===t?"#0f172a":"#ffffff",height:"calc(100vh - 4rem)",overflowY:"auto",overflowX:"hidden"},children:(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold transition-colors duration-300",style:{color:"dark"===t?"#f8fafc":"#1f2937"},children:(()=>{switch(e){case"dashboard":default:return"Dashboard";case"products":return"Product Lists";case"debts":return"Customer Debt Management";case"family-gallery":return"Family Gallery";case"api-graphing":return"API Graphing & Visuals";case"history":return"History";case"calendar":return"Calendar";case"settings":return"Settings";case"ai-support":return"AI Support"}})()}),(0,r.jsx)("p",{className:"mt-2 transition-colors duration-300",style:{color:"dark"===t?"#cbd5e1":"#6b7280"},children:(()=>{switch(e){case"dashboard":default:return"Overview of your Revantad Store";case"products":return"Manage your product lists with CRUD operations";case"debts":return"Track customer debt and payments";case"family-gallery":return"Manage family photos and memories";case"api-graphing":return"Visual analytics and business insights";case"history":return"View transaction and activity history";case"calendar":return"Manage events and schedules";case"settings":return"Configure your store settings";case"ai-support":return"Get intelligent assistance for your store management"}})()})]}),(()=>{switch(e){case"products":return(0,r.jsx)(eN,{onStatsUpdate:d});case"debts":return(0,r.jsx)(eC,{onStatsUpdate:d});case"family-gallery":return(0,r.jsx)(eU,{});case"api-graphing":return(0,r.jsx)(X,{stats:i});case"history":return(0,r.jsx)(e6,{});case"calendar":return(0,r.jsx)(eG,{});case"settings":return(0,r.jsx)(au,{});case"ai-support":return(0,r.jsx)(aT,{});default:return(0,r.jsx)(O,{stats:i,onSectionChange:a})}})()]})})]}),(0,r.jsx)(aS,{context:e})]})})}t(7280)},7280:(e,a,t)=>{"use strict";t.d(a,{ThemeProvider:()=>l});var r=t(5155),s=t(1362);function l(e){let{children:a,...t}=e;return(0,r.jsx)(s.N,{...t,children:a})}},8606:(e,a,t)=>{Promise.resolve().then(t.bind(t,2409))}},e=>{var a=a=>e(e.s=a);e.O(0,[142,872,441,684,358],()=>a(8606)),_N_E=e.O()}]);