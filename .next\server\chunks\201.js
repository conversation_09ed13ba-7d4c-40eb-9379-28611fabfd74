"use strict";exports.id=201,exports.ids=[201],exports.modules={2030:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],i=n[0];if(Array.isArray(r)&&Array.isArray(i)){if(r[0]!==i[0]||r[2]!==i[2])return!0}else if(r!==i)return!0;if(t[4])return!n[4];if(n[4])return!0;let a=Object.values(t[1])[0],s=Object.values(n[1])[0];return!a||!s||e(a,s)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let r=n(19169);function i(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},5144:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let r=n(51550),i=n(59656);var a=i._("_maxConcurrency"),s=i._("_runningCount"),o=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,n,i=new Promise((e,r)=>{t=e,n=r}),a=async()=>{try{r._(this,s)[s]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,s)[s]--,r._(this,l)[l]()}};return r._(this,o)[o].push({promiseFn:i,task:a}),r._(this,l)[l](),i}bump(e){let t=r._(this,o)[o].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,o)[o].splice(t,1)[0];r._(this,o)[o].unshift(e),r._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),r._(this,a)[a]=e,r._(this,s)[s]=0,r._(this,o)[o]=[]}}function c(e){if(void 0===e&&(e=!1),(r._(this,s)[s]<r._(this,a)[a]||e)&&r._(this,o)[o].length>0){var t;null==(t=r._(this,o)[o].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return d},STATIC_STALETIME_MS:function(){return f},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return h}});let r=n(59008),i=n(59154),a=n(75076);function s(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function o(e,t,n){return s(e,t===i.PrefetchKind.FULL,n)}function l(e){let{url:t,nextUrl:n,tree:r,prefetchCache:a,kind:o,allowAliasing:l=!0}=e,u=function(e,t,n,r,a){for(let o of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[n,null])){let n=s(e,!0,o),l=s(e,!1,o),u=e.search?n:l,c=r.get(u);if(c&&a){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let h=r.get(l);if(a&&e.search&&t!==i.PrefetchKind.FULL&&h&&!h.key.includes("%"))return{...h,aliased:!0}}if(t!==i.PrefetchKind.FULL&&a){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,o,n,a,l);return u?(u.status=p(u),u.kind!==i.PrefetchKind.FULL&&o===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:null!=o?o:i.PrefetchKind.TEMPORARY})}),o&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=o),u):c({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:o||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:n,prefetchCache:r,url:a,data:s,kind:l}=e,u=s.couldBeIntercepted?o(a,l,t):o(a,l),c={treeAtTimeOfPrefetch:n,data:Promise.resolve(s),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:s.staleTime,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:a};return r.set(u,c),c}function c(e){let{url:t,kind:n,tree:s,nextUrl:l,prefetchCache:u}=e,c=o(t,n),h=a.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:s,nextUrl:l,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:i}=e,a=r.get(i);if(!a)return;let s=o(t,a.kind,n);return r.set(s,{...a,key:s}),r.delete(i),s}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=n?n:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),d={treeAtTimeOfPrefetch:s,data:h,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,d),d}function h(e){for(let[t,n]of e)p(n)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let d=1e3*Number("0"),f=1e3*Number("300");function p(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:a}=e;return -1!==a?Date.now()<n+a?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+d?r?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<n+f?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<n+f?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let r=n(96127);function i(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7044:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},8830:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(59154),n(25232),n(29651),n(28627),n(78866),n(75076),n(97936),n(35429);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return h},handleAliasedPrefetchEntry:function(){return c}});let r=n(83913),i=n(89752),a=n(86770),s=n(57391),o=n(33123),l=n(33898),u=n(59435);function c(e,t,n,c,d){let f,p=t.tree,m=t.cache,y=(0,s.createHrefFromUrl)(c);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=h(n,Object.fromEntries(c.searchParams));let{seedData:s,isRootRender:u,pathToSegment:d}=t,g=["",...d];n=h(n,Object.fromEntries(c.searchParams));let v=(0,a.applyRouterStatePatchToTree)(g,p,n,y),b=(0,i.createEmptyCacheNode)();if(u&&s){let t=s[1];b.loading=s[3],b.rsc=t,function e(t,n,i,a,s){if(0!==Object.keys(a[1]).length)for(let l in a[1]){let u,c=a[1][l],h=c[0],d=(0,o.createRouterCacheKey)(h),f=null!==s&&void 0!==s[2][l]?s[2][l]:null;if(null!==f){let e=f[1],n=f[3];u={lazyData:null,rsc:h.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let p=n.parallelRoutes.get(l);p?p.set(d,u):n.parallelRoutes.set(l,new Map([[d,u]])),e(t,u,i,c,f)}}(e,b,m,n,s)}else b.rsc=m.rsc,b.prefetchRsc=m.prefetchRsc,b.loading=m.loading,b.parallelRoutes=new Map(m.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,m,t);v&&(p=v,m=b,f=!0)}return!!f&&(d.patchedTree=p,d.cache=m,d.canonicalUrl=y,d.hashFragment=c.hash,(0,u.handleMutable)(t,d))}function h(e,t){let[n,i,...a]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),i,...a];let s={};for(let[e,n]of Object.entries(i))s[e]=h(n,t);return[n,s,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},12157:(e,t,n)=>{n.d(t,{L:()=>r});let r=(0,n(43210).createContext)({})},15124:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(43210);let i=n(7044).B?r.useLayoutEffect:r.useEffect},18171:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(74479);function i(e){return(0,r.G)(e)&&"offsetHeight"in e}},18468:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let s=a.length<=2,[o,l]=a,u=(0,r.createRouterCacheKey)(l),c=n.parallelRoutes.get(o);if(!c)return;let h=t.parallelRoutes.get(o);if(h&&h!==c||(h=new Map(c),t.parallelRoutes.set(o,h)),s)return void h.delete(u);let d=c.get(u),f=h.get(u);f&&d&&(f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes)},h.set(u,f)),e(f,d,(0,i.getNextFlightSegmentPath)(a)))}}});let r=n(33123),i=n(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19169:(e,t)=>{function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},21279:(e,t,n)=>{n.d(t,{t:()=>r});let r=(0,n(43210).createContext)(null)},22308:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,i,,s]=t;for(let o in r.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==s&&(t[2]=n,t[3]="refresh"),i)e(i[o],n)}},refreshInactiveParallelSegments:function(){return s}});let r=n(56928),i=n(59008),a=n(83913);async function s(e){let t=new Set;await o({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function o(e){let{navigatedAt:t,state:n,updatedTree:a,updatedCache:s,includeNextUrl:l,fetchedSegments:u,rootTree:c=a,canonicalUrl:h}=e,[,d,f,p]=a,m=[];if(f&&f!==h&&"refresh"===p&&!u.has(f)){u.add(f);let e=(0,i.fetchServerResponse)(new URL(f,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:l?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,s,s,e)});m.push(e)}for(let e in d){let r=o({navigatedAt:t,state:n,updatedTree:d[e],updatedCache:s,includeNextUrl:l,fetchedSegments:u,rootTree:c,canonicalUrl:h});m.push(r)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24642:(e,t)=>{function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},25232:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,n){let{url:T,isExternalUrl:x,navigateType:w,shouldScroll:R,allowAliasing:E}=n,S={},{hash:_}=T,M=(0,i.createHrefFromUrl)(T),A="push"===w;if((0,y.prunePrefetchCache)(t.prefetchCache),S.preserveCustomHistoryState=!1,S.pendingPush=A,x)return b(t,S,T.toString(),A);if(document.getElementById("__next-page-redirect"))return b(t,S,M,A);let j=(0,y.getOrCreatePrefetchCacheEntry)({url:T,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:E}),{treeAtTimeOfPrefetch:O,data:C}=j;return d.prefetchQueue.bump(C),C.then(d=>{let{flightData:y,canonicalUrl:x,postponed:w}=d,E=Date.now(),C=!1;if(j.lastUsedTime||(j.lastUsedTime=E,C=!0),j.aliased){let r=(0,v.handleAliasedPrefetchEntry)(E,t,y,T,S);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof y)return b(t,S,y,A);let D=x?(0,i.createHrefFromUrl)(x):M;if(_&&t.canonicalUrl.split("#",1)[0]===D.split("#",1)[0])return S.onlyHashChange=!0,S.canonicalUrl=D,S.shouldScroll=R,S.hashFragment=_,S.scrollableSegments=[],(0,c.handleMutable)(t,S);let L=t.tree,k=t.cache,V=[];for(let e of y){let{pathToSegment:n,seedData:i,head:c,isHeadPartial:d,isRootRender:y}=e,v=e.tree,x=["",...n],R=(0,s.applyRouterStatePatchToTree)(x,L,v,M);if(null===R&&(R=(0,s.applyRouterStatePatchToTree)(x,O,v,M)),null!==R){if(i&&y&&w){let e=(0,m.startPPRNavigation)(E,k,L,v,i,c,d,!1,V);if(null!==e){if(null===e.route)return b(t,S,M,A);R=e.route;let n=e.node;null!==n&&(S.cache=n);let i=e.dynamicRequestTree;if(null!==i){let n=(0,r.fetchServerResponse)(T,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,n)}}else R=v}else{if((0,l.isNavigatingToNewRootLayout)(L,R))return b(t,S,M,A);let r=(0,f.createEmptyCacheNode)(),i=!1;for(let t of(j.status!==u.PrefetchCacheEntryStatus.stale||C?i=(0,h.applyFlightData)(E,k,r,e,j):(i=function(e,t,n,r){let i=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),P(r).map(e=>[...n,...e])))(0,g.clearCacheNodeDataForSegmentPath)(e,t,a),i=!0;return i}(r,k,n,v),j.lastUsedTime=E),(0,o.shouldHardNavigate)(x,L)?(r.rsc=k.rsc,r.prefetchRsc=k.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(r,k,n),S.cache=r):i&&(S.cache=r,k=r),P(v))){let e=[...n,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&V.push(e)}}L=R}}return S.patchedTree=L,S.canonicalUrl=D,S.scrollableSegments=V,S.hashFragment=_,S.shouldScroll=R,(0,c.handleMutable)(t,S)},()=>t)}}});let r=n(59008),i=n(57391),a=n(18468),s=n(86770),o=n(65951),l=n(2030),u=n(59154),c=n(59435),h=n(56928),d=n(75076),f=n(89752),p=n(83913),m=n(65956),y=n(5334),g=n(97464),v=n(9707);function b(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function P(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,i]of Object.entries(r))for(let r of P(i))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,n)=>{function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26001:(e,t,n)=>{let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function a(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function s(e,t,n,r){if("function"==typeof t){let[i,s]=a(r);t=t(void 0!==n?n:e.custom,i,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,s]=a(r);t=t(void 0!==n?n:e.custom,i,s)}return t}function o(e,t,n){let r=e.getProps();return s(r,t,void 0!==n?n:r.custom,e)}function l(e,t){return e?.[t]??e?.default??e}n.d(t,{P:()=>a_});let u=e=>e,c={},h=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],d={value:null,addProjectionMetrics:null};function f(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},a=()=>n=!0,s=h.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,a=!1,s=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){s.has(t)&&(c.schedule(t),e()),l++,t(o)}let c={schedule:(e,t=!1,a=!1)=>{let o=a&&i?n:r;return t&&s.add(e),o.has(e)||o.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{if(o=e,i){a=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&d.value&&d.value.frameloop[t].push(l),l=0,n.clear(),i=!1,a&&(a=!1,c.process(e))}};return c}(a,t?n:void 0),e),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:f,update:p,preRender:m,render:y,postRender:g}=s,v=()=>{let a=c.useManualTiming?i.timestamp:performance.now();n=!1,c.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(a-i.timestamp,40),1)),i.timestamp=a,i.isProcessing=!0,o.process(i),l.process(i),u.process(i),f.process(i),p.process(i),m.process(i),y.process(i),g.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(v))},b=()=>{n=!0,r=!0,i.isProcessing||e(v)};return{schedule:h.reduce((e,t)=>{let r=s[t];return e[t]=(e,t=!1,i=!1)=>(n||b(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<h.length;t++)s[h[t]].cancel(e)},state:i,steps:s}}let{schedule:p,cancel:m,state:y,steps:g}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(v),P=new Set(["width","height","top","left","right","bottom",...v]);function T(e,t){-1===e.indexOf(t)&&e.push(t)}function x(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class w{constructor(){this.subscriptions=[]}add(e){return T(this.subscriptions,e),()=>x(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function R(){r=void 0}let E={now:()=>(void 0===r&&E.set(y.isProcessing||c.useManualTiming?y.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(R)}},S=e=>!isNaN(parseFloat(e)),_={current:void 0};class M{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=E.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=E.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new w);let n=this.events[e].add(t);return"change"===e?()=>{n(),p.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return _.current&&_.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=E.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function A(e,t){return new M(e,t)}let j=e=>Array.isArray(e),O=e=>!!(e&&e.getVelocity);function C(e,t){let n=e.getValue("willChange");if(O(n)&&n.add)return n.add(t);if(!n&&c.WillChange){let n=new c.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let D=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+D("framerAppearId"),k=(e,t)=>n=>t(e(n)),V=(...e)=>e.reduce(k),N=(e,t,n)=>n>t?t:n<e?e:n,U=e=>1e3*e,I=e=>e/1e3,F={layout:0,mainThread:0,waapi:0},B=()=>{},H=()=>{},W=e=>t=>"string"==typeof t&&t.startsWith(e),K=W("--"),z=W("var(--"),$=e=>!!z(e)&&Y.test(e.split("/*")[0].trim()),Y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,X={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},G={...X,transform:e=>N(0,1,e)},q={...X,default:1},Z=e=>Math.round(1e5*e)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>n=>!!("string"==typeof n&&J.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),et=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,a,s,o]=r.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(a),[n]:parseFloat(s),alpha:void 0!==o?parseFloat(o):1}},en=e=>N(0,255,e),er={...X,transform:e=>Math.round(en(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+er.transform(e)+", "+er.transform(t)+", "+er.transform(n)+", "+Z(G.transform(r))+")"},ea={test:ee("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},es=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eo=es("deg"),el=es("%"),eu=es("px"),ec=es("vh"),eh=es("vw"),ed={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ef={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(n))+", "+Z(G.transform(r))+")"},ep={test:e=>ei.test(e)||ea.test(e)||ef.test(e),parse:e=>ei.test(e)?ei.parse(e):ef.test(e)?ef.parse(e):ea.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ef.transform(e),getAnimatableNone:e=>{let t=ep.parse(e);return t.alpha=0,ep.transform(t)}},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ey="number",eg="color",ev=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eb(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],a=0,s=t.replace(ev,e=>(ep.test(e)?(r.color.push(a),i.push(eg),n.push(ep.parse(e))):e.startsWith("var(")?(r.var.push(a),i.push("var"),n.push(e)):(r.number.push(a),i.push(ey),n.push(parseFloat(e))),++a,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function eP(e){return eb(e).values}function eT(e){let{split:t,types:n}=eb(e),r=t.length;return e=>{let i="";for(let a=0;a<r;a++)if(i+=t[a],void 0!==e[a]){let t=n[a];t===ey?i+=Z(e[a]):t===eg?i+=ep.transform(e[a]):i+=e[a]}return i}}let ex=e=>"number"==typeof e?0:ep.test(e)?ep.getAnimatableNone(e):e,ew={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Q)?.length||0)+(e.match(em)?.length||0)>0},parse:eP,createTransformer:eT,getAnimatableNone:function(e){let t=eP(e);return eT(e)(t.map(ex))}};function eR(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function eE(e,t){return n=>n>0?t:e}let eS=(e,t,n)=>e+(t-e)*n,e_=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},eM=[ea,ei,ef],eA=e=>eM.find(t=>t.test(e));function ej(e){let t=eA(e);if(B(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===ef&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,a=0,s=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,o=2*n-r;i=eR(o,r,e+1/3),a=eR(o,r,e),s=eR(o,r,e-1/3)}else i=a=s=n;return{red:Math.round(255*i),green:Math.round(255*a),blue:Math.round(255*s),alpha:r}}(n)),n}let eO=(e,t)=>{let n=ej(e),r=ej(t);if(!n||!r)return eE(e,t);let i={...n};return e=>(i.red=e_(n.red,r.red,e),i.green=e_(n.green,r.green,e),i.blue=e_(n.blue,r.blue,e),i.alpha=eS(n.alpha,r.alpha,e),ei.transform(i))},eC=new Set(["none","hidden"]);function eD(e,t){return n=>eS(e,t,n)}function eL(e){return"number"==typeof e?eD:"string"==typeof e?$(e)?eE:ep.test(e)?eO:eN:Array.isArray(e)?ek:"object"==typeof e?ep.test(e)?eO:eV:eE}function ek(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>eL(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eV(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=eL(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eN=(e,t)=>{let n=ew.createTransformer(t),r=eb(e),i=eb(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?eC.has(e)&&!i.values.length||eC.has(t)&&!r.values.length?function(e,t){return eC.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):V(ek(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let a=t.types[i],s=e.indexes[a][r[a]],o=e.values[s]??0;n[i]=o,r[a]++}return n}(r,i),i.values),n):(B(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eE(e,t))};function eU(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?eS(e,t,n):eL(e)(e,t)}let eI=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>p.update(t,e),stop:()=>m(t),now:()=>y.isProcessing?y.timestamp:E.now()}},eF=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function eB(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function eH(e,t,n){var r,i;let a=Math.max(t-5,0);return r=n-e(a),(i=t-a)?1e3/i*r:0}let eW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eK(e,t){return e*Math.sqrt(1-t*t)}let ez=["duration","bounce"],e$=["stiffness","damping","mass"];function eY(e,t){return t.some(t=>void 0!==e[t])}function eX(e=eW.visualDuration,t=eW.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:a}=r,s=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:h,duration:d,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:eW.velocity,stiffness:eW.stiffness,damping:eW.damping,mass:eW.mass,isResolvedFromDuration:!1,...e};if(!eY(e,e$)&&eY(e,ez))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*N(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:eW.mass,stiffness:r,damping:i}}else{let n=function({duration:e=eW.duration,bounce:t=eW.bounce,velocity:n=eW.velocity,mass:r=eW.mass}){let i,a;B(e<=U(eW.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=N(eW.minDamping,eW.maxDamping,s),e=N(eW.minDuration,eW.maxDuration,I(e)),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/eK(t,s)*Math.exp(-i)},a=t=>{let r=t*s*e,a=Math.pow(s,2)*Math.pow(t,2)*e,o=Math.exp(-r),l=eK(Math.pow(t,2),s);return(r*n+n-a)*o*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),a=t=>e*e*(n-t)*Math.exp(-t*e));let o=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,a,5/e);if(e=U(e),isNaN(o))return{stiffness:eW.stiffness,damping:eW.damping,duration:e};{let t=Math.pow(o,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:eW.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-I(r.velocity||0)}),m=f||0,y=c/(2*Math.sqrt(u*h)),g=o-s,v=I(Math.sqrt(u/h)),b=5>Math.abs(g);if(i||(i=b?eW.restSpeed.granular:eW.restSpeed.default),a||(a=b?eW.restDelta.granular:eW.restDelta.default),y<1){let e=eK(v,y);n=t=>o-Math.exp(-y*v*t)*((m+y*v*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}else if(1===y)n=e=>o-Math.exp(-v*e)*(g+(m+v*g)*e);else{let e=v*Math.sqrt(y*y-1);n=t=>{let n=Math.exp(-y*v*t),r=Math.min(e*t,300);return o-n*((m+y*v*g)*Math.sinh(r)+e*g*Math.cosh(r))/e}}let P={calculatedDuration:p&&d||null,next:e=>{let t=n(e);if(p)l.done=e>=d;else{let r=0===e?m:0;y<1&&(r=0===e?U(m):eH(n,e,t));let s=Math.abs(o-t)<=a;l.done=Math.abs(r)<=i&&s}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(eB(P),2e4),t=eF(t=>P.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return P}function eG({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:a=500,modifyTarget:s,min:o,max:l,restDelta:u=.5,restSpeed:c}){let h,d,f=e[0],p={done:!1,value:f},m=e=>void 0!==o&&e<o||void 0!==l&&e>l,y=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,g=n*t,v=f+g,b=void 0===s?v:s(v);b!==v&&(g=b-f);let P=e=>-g*Math.exp(-e/r),T=e=>b+P(e),x=e=>{let t=P(e),n=T(e);p.done=Math.abs(t)<=u,p.value=p.done?b:n},w=e=>{m(p.value)&&(h=e,d=eX({keyframes:[p.value,y(p.value)],velocity:eH(T,e,p.value),damping:i,stiffness:a,restDelta:u,restSpeed:c}))};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==h||(t=!0,x(e),w(e)),void 0!==h&&e>=h)?d.next(e-h):(t||x(e),p)}}}eX.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(eB(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:I(i)}}(e,100,eX);return e.ease=t.ease,e.duration=U(t.duration),e.type="keyframes",e};let eq=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eZ(e,t,n,r){if(e===t&&n===r)return u;let i=t=>(function(e,t,n,r,i){let a,s,o=0;do(a=eq(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(a)>1e-7&&++o<12);return s})(t,0,1,e,n);return e=>0===e||1===e?e:eq(i(e),t,r)}let eQ=eZ(.42,0,1,1),eJ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e3=e=>t=>1-e(1-t),e5=eZ(.33,1.53,.69,.99),e9=e3(e5),e4=e2(e9),e6=e=>(e*=2)<1?.5*e9(e):.5*(2-Math.pow(2,-10*(e-1))),e7=e=>1-Math.sin(Math.acos(e)),e8=e3(e7),te=e2(e7),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tn={linear:u,easeIn:eQ,easeInOut:e0,easeOut:eJ,circIn:e7,circInOut:te,circOut:e8,backIn:e9,backInOut:e4,backOut:e5,anticipate:e6},tr=e=>"string"==typeof e,ti=e=>{if(tt(e)){H(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return eZ(t,n,r,i)}return tr(e)?(H(void 0!==tn[e],`Invalid easing type '${e}'`),tn[e]):e},ta=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function ts({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let a=e1(r)?r.map(ti):ti(r),s={done:!1,value:t[0]},o=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let a=e.length;if(H(a===t.length,"Both input and output ranges must be the same length"),1===a)return()=>t[0];if(2===a&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,n){let r=[],i=n||c.mix||eU,a=e.length-1;for(let n=0;n<a;n++){let a=i(e[n],e[n+1]);t&&(a=V(Array.isArray(t)?t[n]||u:t,a)),r.push(a)}return r}(t,r,i),l=o.length,h=n=>{if(s&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=ta(e[r],e[r+1],n);return o[r](i)};return n?t=>h(N(e[0],e[a-1],t)):h}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=ta(0,t,r);e.push(eS(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(a)?a:t.map(()=>a||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=o(t),s.done=t>=e,s)}}let to=e=>null!==e;function tl(e,{repeat:t,repeatType:n="loop"},r,i=1){let a=e.filter(to),s=i<0||t&&"loop"!==n&&t%2==1?0:a.length-1;return s&&void 0!==r?r:a[s]}let tu={decay:eG,inertia:eG,tween:ts,keyframes:ts,spring:eX};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class th{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let td=e=>e/100;class tf extends th{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==E.now()&&this.tick(E.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},F.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ts,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:a=0}=e,{keyframes:s}=e,o=t||ts;o!==ts&&"number"!=typeof s[0]&&(this.mixKeyframes=V(td,eU(s[0],s[1])),s=[0,100]);let l=o({...e,keyframes:s});"mirror"===i&&(this.mirroredGenerator=o({...e,keyframes:[...s].reverse(),velocity:-a})),null===l.calculatedDuration&&(l.calculatedDuration=eB(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:a,resolvedDuration:s,calculatedDuration:o}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:h,repeatDelay:d,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>r;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=n;if(c){let e=Math.min(this.currentTime,r)/s,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===h?(n=1-n,d&&(n-=d/s)):"mirror"===h&&(b=a)),v=N(0,1,n)*s}let P=g?{done:!1,value:u[0]}:b.next(v);i&&(P.value=i(P.value));let{done:T}=P;g||null===o||(T=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let x=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return x&&f!==eG&&(P.value=tl(u,this.options,m,this.speed)),p&&p(P.value),x&&this.finish(),P}then(e,t){return this.finished.then(e,t)}get duration(){return I(this.calculatedDuration)}get time(){return I(this.currentTime)}set time(e){e=U(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(E.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=I(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eI,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(E.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,F.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tp=e=>180*e/Math.PI,tm=e=>tg(tp(Math.atan2(e[1],e[0]))),ty={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tp(Math.atan(e[1])),skewY:e=>tp(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tg=e=>((e%=360)<0&&(e+=360),e),tv=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tb=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tP={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tv,scaleY:tb,scale:e=>(tv(e)+tb(e))/2,rotateX:e=>tg(tp(Math.atan2(e[6],e[5]))),rotateY:e=>tg(tp(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tp(Math.atan(e[4])),skewY:e=>tp(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tT(e){return+!!e.includes("scale")}function tx(e,t){let n,r;if(!e||"none"===e)return tT(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tP,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=ty,r=t}if(!r)return tT(t);let a=n[t],s=r[1].split(",").map(tR);return"function"==typeof a?a(s):s[a]}let tw=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tx(n,t)};function tR(e){return parseFloat(e.trim())}let tE=e=>e===X||e===eu,tS=new Set(["x","y","z"]),t_=v.filter(e=>!tS.has(e)),tM={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tx(t,"x"),y:(e,{transform:t})=>tx(t,"y")};tM.translateX=tM.x,tM.translateY=tM.y;let tA=new Set,tj=!1,tO=!1,tC=!1;function tD(){if(tO){let e=Array.from(tA).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return t_.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tO=!1,tj=!1,tA.forEach(e=>e.complete(tC)),tA.clear()}function tL(){tA.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tO=!0)})}class tk{constructor(e,t,n,r,i,a=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=a}scheduleResolve(){this.state="scheduled",this.isAsync?(tA.add(this),tj||(tj=!0,p.read(tL),p.resolveKeyframes(tD))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),a=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,a);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=a),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tA.delete(this)}cancel(){"scheduled"===this.state&&(tA.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tV=e=>e.startsWith("--");function tN(e){let t;return()=>(void 0===t&&(t=e()),t)}let tU=tN(()=>void 0!==window.ScrollTimeline),tI={},tF=function(e,t){let n=tN(e);return()=>tI[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tB=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,tH={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tB([0,.65,.55,1]),circOut:tB([.55,0,1,.45]),backIn:tB([.31,.01,.66,-.59]),backOut:tB([.33,1.53,.69,.99])};function tW(e){return"function"==typeof e&&"applyToOptions"in e}class tK extends th{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:a=!1,finalKeyframe:s,onComplete:o}=e;this.isPseudoElement=!!i,this.allowFlatten=a,this.options=e,H("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tW(e)&&tF()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:a=0,repeatType:s="loop",ease:o="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let h=function e(t,n){if(t)return"function"==typeof t?tF()?eF(t,n):"ease-out":tt(t)?tB(t):Array.isArray(t)?t.map(t=>e(t,n)||tH.easeOut):tH[t]}(o,i);Array.isArray(h)&&(c.easing=h),d.value&&F.waapi++;let f={delay:r,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:a+1,direction:"reverse"===s?"alternate":"normal"};u&&(f.pseudoElement=u);let p=e.animate(c,f);return d.value&&p.finished.finally(()=>{F.waapi--}),p}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tV(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return I(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return I(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=U(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tU())?(this.animation.timeline=e,u):t(this)}}let tz={anticipate:e6,backInOut:e4,circInOut:te};class t$ extends tK{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tz&&(e.ease=tz[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...a}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new tf({...a,autoplay:!1}),o=U(this.finishedTime??this.time);t.setWithVelocity(s.sample(o-10).value,s.sample(o).value,10),s.stop()}}let tY=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(ew.test(e)||"0"===e)&&!e.startsWith("url("));var tX,tG,tq=n(18171);let tZ=new Set(["opacity","clipPath","filter","transform"]),tQ=tN(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends th{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:a="loop",keyframes:s,name:o,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=E.now();let h={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:a,name:o,motionValue:l,element:u,...c},d=u?.KeyframeResolver||tk;this.keyframeResolver=new d(s,(e,t,n)=>this.onKeyframesResolved(e,t,h,!n),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:a,velocity:s,delay:o,isHandoff:l,onUpdate:h}=n;this.resolvedAt=E.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let a=e[e.length-1],s=tY(i,t),o=tY(a,t);return B(s===o,`You are trying to animate ${t} from "${i}" to "${a}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${a} via the \`style\` property.`),!!s&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||tW(n))&&r)}(e,i,a,s)&&((c.instantAnimations||!o)&&h?.(tl(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let d={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},f=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:a,type:s}=e;if(!(0,tq.s)(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return tQ()&&n&&tZ.has(n)&&("transform"!==n||!l)&&!o&&!r&&"mirror"!==i&&0!==a&&"inertia"!==s}(d)?new t$({...d,element:d.motionValue.owner.current}):new tf(d);f.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tC=!0,tL(),tD(),tC=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t3={type:"keyframes",duration:.8},t5={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t9=(e,{keyframes:t})=>t.length>2?t3:b.has(e)?e.startsWith("scale")?t2(t[1]):t1:t5,t4=(e,t,n,r={},i,a)=>s=>{let o=l(r,e)||{},u=o.delay||r.delay||0,{elapsed:h=0}=r;h-=U(u);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-h,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{s(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:a?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:a,repeatType:s,repeatDelay:o,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)&&Object.assign(d,t9(e,d)),d.duration&&(d.duration=U(d.duration)),d.repeatDelay&&(d.repeatDelay=U(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let f=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(f=!0)),(c.instantAnimations||c.skipAnimations)&&(f=!0,d.duration=0,d.delay=0),d.allowFlatten=!o.type&&!o.ease,f&&!a&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(t0),a=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[a]}(d.keyframes,o);if(void 0!==e)return void p.update(()=>{d.onUpdate(e),d.onComplete()})}return o.isSync?new tf(d):new tJ(d)};function t6(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:a=e.getDefaultTransition(),transitionEnd:s,...u}=t;r&&(a=r);let c=[],h=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||h&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(h,t))continue;let s={delay:n,...l(a||{},t)},o=r.get();if(void 0!==o&&!r.isAnimating&&!Array.isArray(i)&&i===o&&!s.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let n=e.props[L];if(n){let e=window.MotionHandoffAnimation(n,t,p);null!==e&&(s.startTime=e,d=!0)}}C(e,t),r.start(t4(t,r,i,e.shouldReduceMotion&&P.has(t)?{type:!1}:s,e,d));let f=r.animation;f&&c.push(f)}return s&&Promise.all(c).then(()=>{p.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=o(e,t)||{};for(let t in i={...i,...n}){var a;let n=j(a=i[t])?a[a.length-1]||0:a;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,A(n))}}(e,s)})}),c}function t7(e,t,n={}){let r=o(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let a=r?()=>Promise.all(t6(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:a=0,staggerChildren:s,staggerDirection:o}=i;return function(e,t,n=0,r=0,i=0,a=1,s){let o=[],l=e.variantChildren.size,u=(l-1)*i,c="function"==typeof r,h=c?e=>r(e,l):1===a?(e=0)=>e*i:(e=0)=>u-e*i;return Array.from(e.variantChildren).sort(t8).forEach((e,i)=>{e.notify("AnimationStart",t),o.push(t7(e,t,{...s,delay:n+(c?0:r)+h(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,r,a,s,o,n)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([a(),s(n.delay)]);{let[e,t]="beforeChildren"===l?[a,s]:[s,a];return e().then(()=>t())}}function t8(e,t){return e.sortNodePosition(t)}function ne(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function nt(e){return"string"==typeof e||Array.isArray(e)}let nn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nr=["initial",...nn],ni=nr.length,na=[...nn].reverse(),ns=nn.length;function no(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nl(){return{animate:no(!0),whileInView:no(),whileHover:no(),whileTap:no(),whileDrag:no(),whileFocus:no(),exit:no()}}class nu{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nc extends nu{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>t7(e,t,n)));else if("string"==typeof t)r=t7(e,t,n);else{let i="function"==typeof t?o(e,t,n.custom):t;r=Promise.all(t6(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nl(),r=!0,a=t=>(n,r)=>{let i=o(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function s(s){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<ni;e++){let r=nr[e],i=t.props[r];(nt(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],h=new Set,d={},f=1/0;for(let t=0;t<ns;t++){var p,m;let o=na[t],y=n[o],g=void 0!==l[o]?l[o]:u[o],v=nt(g),b=o===s?y.isActive:null;!1===b&&(f=t);let P=g===u[o]&&g!==l[o]&&v;if(P&&r&&e.manuallyAnimateOnMount&&(P=!1),y.protectedKeys={...d},!y.isActive&&null===b||!g&&!y.prevProp||i(g)||"boolean"==typeof g)continue;let T=(p=y.prevProp,"string"==typeof(m=g)?m!==p:!!Array.isArray(m)&&!ne(m,p)),x=T||o===s&&y.isActive&&!P&&v||t>f&&v,w=!1,R=Array.isArray(g)?g:[g],E=R.reduce(a(o),{});!1===b&&(E={});let{prevResolvedValues:S={}}=y,_={...S,...E},M=t=>{x=!0,h.has(t)&&(w=!0,h.delete(t)),y.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in _){let t=E[e],n=S[e];if(d.hasOwnProperty(e))continue;let r=!1;(j(t)&&j(n)?ne(t,n):t===n)?void 0!==t&&h.has(e)?M(e):y.protectedKeys[e]=!0:null!=t?M(e):h.add(e)}y.prevProp=g,y.prevResolvedValues=E,y.isActive&&(d={...d,...E}),r&&e.blockInitialAnimation&&(x=!1);let A=!(P&&T)||w;x&&A&&c.push(...R.map(e=>({animation:e,options:{type:o}})))}if(h.size){let t={};if("boolean"!=typeof l.initial){let n=o(e,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(t.transition=n.transition)}h.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let y=!!c.length;return r&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(y=!1),r=!1,y?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=s(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nl(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nh=0;class nd extends nu{constructor(){super(...arguments),this.id=nh++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let nf={x:!1,y:!1};function np(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nm=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ny(e){return{point:{x:e.pageX,y:e.pageY}}}let ng=e=>t=>nm(t)&&e(t,ny(t));function nv(e,t,n,r){return np(e,t,ng(n),r)}function nb({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nP(e){return e.max-e.min}function nT(e,t,n,r=.5){e.origin=r,e.originPoint=eS(t.min,t.max,e.origin),e.scale=nP(n)/nP(t),e.translate=eS(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nx(e,t,n,r){nT(e.x,t.x,n.x,r?r.originX:void 0),nT(e.y,t.y,n.y,r?r.originY:void 0)}function nw(e,t,n){e.min=n.min+t.min,e.max=e.min+nP(t)}function nR(e,t,n){e.min=t.min-n.min,e.max=e.min+nP(t)}function nE(e,t,n){nR(e.x,t.x,n.x),nR(e.y,t.y,n.y)}let nS=()=>({translate:0,scale:1,origin:0,originPoint:0}),n_=()=>({x:nS(),y:nS()}),nM=()=>({min:0,max:0}),nA=()=>({x:nM(),y:nM()});function nj(e){return[e("x"),e("y")]}function nO(e){return void 0===e||1===e}function nC({scale:e,scaleX:t,scaleY:n}){return!nO(e)||!nO(t)||!nO(n)}function nD(e){return nC(e)||nL(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nL(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nk(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nV(e,t=0,n=1,r,i){e.min=nk(e.min,t,n,r,i),e.max=nk(e.max,t,n,r,i)}function nN(e,{x:t,y:n}){nV(e.x,t.translate,t.scale,t.originPoint),nV(e.y,n.translate,n.scale,n.originPoint)}function nU(e,t){e.min=e.min+t,e.max=e.max+t}function nI(e,t,n,r,i=.5){let a=eS(e.min,e.max,i);nV(e,t,n,a,r)}function nF(e,t){nI(e.x,t.x,t.scaleX,t.scale,t.originX),nI(e.y,t.y,t.scaleY,t.scale,t.originY)}function nB(e,t){return nb(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let nH=({current:e})=>e?e.ownerDocument.defaultView:null;function nW(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let nK=(e,t)=>Math.abs(e-t);class nz{constructor(e,t,{transformPagePoint:n,contextWindow:r=window,dragSnapToOrigin:i=!1,distanceThreshold:a=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nX(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(nK(e.x,t.x)**2+nK(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=y;this.history.push({...r,timestamp:i});let{onStart:a,onMove:s}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=n$(t,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=nX("pointercancel"===e.type?this.lastMoveEventInfo:n$(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,a),r&&r(e,a)},!nm(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.distanceThreshold=a,this.contextWindow=r||window;let s=n$(ny(e),this.transformPagePoint),{point:o}=s,{timestamp:l}=y;this.history=[{...o,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,nX(s,this.history)),this.removeListeners=V(nv(this.contextWindow,"pointermove",this.handlePointerMove),nv(this.contextWindow,"pointerup",this.handlePointerUp),nv(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function n$(e,t){return t?{point:t(e.point)}:e}function nY(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nX({point:e},t){return{point:e,delta:nY(e,nG(t)),offset:nY(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nG(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>U(.1)));)n--;if(!r)return{x:0,y:0};let a=I(i.timestamp-r.timestamp);if(0===a)return{x:0,y:0};let s={x:(i.x-r.x)/a,y:(i.y-r.y)/a};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function nG(e){return e[e.length-1]}function nq(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nZ(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nQ(e,t,n){return{min:nJ(e,t),max:nJ(e,n)}}function nJ(e,t){return"number"==typeof e?e:e[t]||0}let n0=new WeakMap;class n1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nA(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:n}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new nz(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ny(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(nf[e])return null;else return nf[e]=!0,()=>{nf[e]=!1};return nf.x||nf.y?null:(nf.x=nf.y=!0,()=>{nf.x=nf.y=!1})}(n),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nj(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nP(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&p.postRender(()=>i(e,t)),C(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:a}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),a&&a(e,t)},onSessionEnd:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>nj(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:n,contextWindow:nH(this.visualElement)})}stop(e,t){let n=e||this.latestPointerEvent,r=t||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!r||!n)return;let{velocity:a}=r;this.startAnimation(a);let{onDragEnd:s}=this.getProps();s&&p.postRender(()=>s(n,r))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!n2(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),a=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(a=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?eS(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?eS(n,e,r.max):Math.min(e,n)),e}(a,this.constraints[e],this.elastic[e])),i.set(a)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&nW(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nq(e.x,n,i),y:nq(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nQ(e,"left","right"),y:nQ(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nj(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nW(t))return!1;let r=t.current;H(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let a=function(e,t,n){let r=nB(e,n),{scroll:i}=t;return i&&(nU(r.x,i.offset.x),nU(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:nZ(e.x,a.x),y:nZ(e.y,a.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=nb(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:s}=this.getProps(),o=this.constraints||{};return Promise.all(nj(s=>{if(!n2(s,t,this.currentDirection))return;let l=o&&o[s]||{};a&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return C(this.visualElement,e),n.start(t4(e,n,0,t,this.visualElement,!1))}stopAnimation(){nj(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nj(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nj(t=>{let{drag:n}=this.getProps();if(!n2(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:a}=r.layout.layoutBox[t];i.set(e[t]-eS(n,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nW(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};nj(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nP(e),i=nP(t);return i>r?n=ta(t.min,t.max-r,e.min):r>i&&(n=ta(e.min,e.max-i,t.min)),N(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nj(t=>{if(!n2(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:a}=this.constraints[t];n.set(eS(i,a,r[t]))})}addListeners(){if(!this.visualElement.current)return;n0.set(this.visualElement,this);let e=nv(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nW(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),p.read(t);let i=np(window,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nj(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:a=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:a,dragMomentum:s}}}function n2(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class n3 extends nu{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new n1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n5=e=>(t,n)=>{e&&p.postRender(()=>e(t,n))};class n9 extends nu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new nz(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nH(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:n5(e),onStart:n5(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&p.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=nv(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n4=n(60687);let{schedule:n6}=f(queueMicrotask,!1);var n7=n(43210),n8=n(86044),re=n(12157);let rt=(0,n7.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ri={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let n=rr(e,t.target.x),r=rr(e,t.target.y);return`${n}% ${r}%`}},ra={},rs=!1;class ro extends n7.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in ru)ra[e]=ru[e],K(e)&&(ra[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),rs&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:a}=n;return a&&(a.isPresent=i,rs=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?a.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?a.promote():a.relegate()||p.postRender(()=>{let e=a.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n6.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rl(e){let[t,n]=(0,n8.xQ)(),r=(0,n7.useContext)(re.L);return(0,n4.jsx)(ro,{...e,layoutGroup:r,switchLayoutGroup:(0,n7.useContext)(rt),isPresent:t,safeToRemove:n})}let ru={borderRadius:{...ri,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ri,borderTopRightRadius:ri,borderBottomLeftRadius:ri,borderBottomRightRadius:ri,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=ew.parse(e);if(r.length>5)return e;let i=ew.createTransformer(e),a=+("number"!=typeof r[0]),s=n.x.scale*t.x,o=n.y.scale*t.y;r[0+a]/=s,r[1+a]/=o;let l=eS(s,o,.5);return"number"==typeof r[2+a]&&(r[2+a]/=l),"number"==typeof r[3+a]&&(r[3+a]/=l),i(r)}}};var rc=n(74479);function rh(e){return(0,rc.G)(e)&&"ownerSVGElement"in e}let rd=(e,t)=>e.depth-t.depth;class rf{constructor(){this.children=[],this.isDirty=!1}add(e){T(this.children,e),this.isDirty=!0}remove(e){x(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rd),this.isDirty=!1,this.children.forEach(e)}}function rp(e){return O(e)?e.get():e}let rm=["TopLeft","TopRight","BottomLeft","BottomRight"],ry=rm.length,rg=e=>"string"==typeof e?parseFloat(e):e,rv=e=>"number"==typeof e||eu.test(e);function rb(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rP=rx(0,.5,e8),rT=rx(.5,.95,u);function rx(e,t,n){return r=>r<e?0:r>t?1:n(ta(e,t,r))}function rw(e,t){e.min=t.min,e.max=t.max}function rR(e,t){rw(e.x,t.x),rw(e.y,t.y)}function rE(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rS(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function r_(e,t,[n,r,i],a,s){!function(e,t=0,n=1,r=.5,i,a=e,s=e){if(el.test(t)&&(t=parseFloat(t),t=eS(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let o=eS(a.min,a.max,r);e===a&&(o-=t),e.min=rS(e.min,t,n,o,i),e.max=rS(e.max,t,n,o,i)}(e,t[n],t[r],t[i],t.scale,a,s)}let rM=["x","scaleX","originX"],rA=["y","scaleY","originY"];function rj(e,t,n,r){r_(e.x,t,rM,n?n.x:void 0,r?r.x:void 0),r_(e.y,t,rA,n?n.y:void 0,r?r.y:void 0)}function rO(e){return 0===e.translate&&1===e.scale}function rC(e){return rO(e.x)&&rO(e.y)}function rD(e,t){return e.min===t.min&&e.max===t.max}function rL(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rk(e,t){return rL(e.x,t.x)&&rL(e.y,t.y)}function rV(e){return nP(e.x)/nP(e.y)}function rN(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rU{constructor(){this.members=[]}add(e){T(this.members,e),e.scheduleRender()}remove(e){if(x(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rI={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rF=["","X","Y","Z"],rB=0;function rH(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function rW({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rB++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,d.value&&(rI.nodes=rI.calculatedTargetDeltas=rI.calculatedProjections=0),this.nodes.forEach(r$),this.nodes.forEach(rJ),this.nodes.forEach(r0),this.nodes.forEach(rY),d.addProjectionMetrics&&d.addProjectionMetrics(rI)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rf)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new w),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rh(t)&&!(rh(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=0,i=()=>this.root.updateBlockedByResize=!1;p.read(()=>{r=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=E.now(),r=({timestamp:i})=>{let a=i-n;a>=250&&(m(r),e(a-t))};return p.setup(r,!0),()=>m(r)}(i,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(rQ)))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let a=this.options.transition||i.getDefaultTransition()||r4,{onLayoutAnimationStart:s,onLayoutAnimationComplete:o}=i.getProps(),u=!this.targetLayout||!rk(this.targetLayout,r),c=!t&&n;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(a,"layout"),onPlay:s,onComplete:o};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||rQ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[L];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",p,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rG);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(rq);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(rZ),this.nodes.forEach(rK),this.nodes.forEach(rz)):this.nodes.forEach(rq),this.clearAllSnapshots();let e=E.now();y.delta=N(0,1e3/60,e-y.timestamp),y.timestamp=e,y.isProcessing=!0,g.update.process(y),g.preRender.process(y),g.render.process(y),y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n6.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rX),this.sharedNodes.forEach(r2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nP(this.snapshot.measuredBox.x)||nP(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nA(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rC(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,a=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nD(this.latestValues)||a)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),r8((t=r).x),r8(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nA();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(nU(t.x,e.offset.x),nU(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nA();if(rR(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:a}=r;r!==this.root&&i&&a.layoutScroll&&(i.wasRoot&&rR(t,e),nU(t.x,i.offset.x),nU(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nA();rR(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nF(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nD(r.latestValues)&&nF(n,r.latestValues)}return nD(this.latestValues)&&nF(n,this.latestValues),n}removeTransform(e){let t=nA();rR(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nD(n.latestValues))continue;nC(n.latestValues)&&n.updateSnapshot();let r=nA();rR(r,n.measurePageBox()),rj(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nD(this.latestValues)&&rj(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=y.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nA(),this.relativeTargetOrigin=nA(),nE(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rR(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nA(),this.targetWithTransforms=nA()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var a,s,o;this.forceRelativeParentToResolveTarget(),a=this.target,s=this.relativeTarget,o=this.relativeParent.target,nw(a.x,s.x,o.x),nw(a.y,s.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rR(this.target,this.layout.layoutBox),nN(this.target,this.targetDelta)):rR(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nA(),this.relativeTargetOrigin=nA(),nE(this.relativeTargetOrigin,this.target,e.target),rR(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}d.value&&rI.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nC(this.parent.latestValues)||nL(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===y.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rR(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){let i,a,s=n.length;if(s){t.x=t.y=1;for(let o=0;o<s;o++){a=(i=n[o]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nF(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,nN(e,a)),r&&nD(i.latestValues)&&nF(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nA());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rE(this.prevProjectionDelta.x,this.projectionDelta.x),rE(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nx(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===a&&this.treeScale.y===s&&rN(this.projectionDelta.x,this.prevProjectionDelta.x)&&rN(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),d.value&&rI.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=n_(),this.projectionDelta=n_(),this.projectionDeltaWithTransform=n_()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},a={...this.latestValues},s=n_();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=nA(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,h=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r9));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r3(s.x,e.x,r),r3(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,f,p,m,y;nE(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=o,y=r,r5(f.x,p.x,m.x,y),r5(f.y,p.y,m.y,y),n&&(u=this.relativeTarget,d=n,rD(u.x,d.x)&&rD(u.y,d.y))&&(this.isProjectionDirty=!1),n||(n=nA()),rR(n,this.relativeTarget)}l&&(this.animationValues=a,function(e,t,n,r,i,a){i?(e.opacity=eS(0,n.opacity??1,rP(r)),e.opacityExit=eS(t.opacity??1,0,rT(r))):a&&(e.opacity=eS(t.opacity??1,n.opacity??1,r));for(let i=0;i<ry;i++){let a=`border${rm[i]}Radius`,s=rb(t,a),o=rb(n,a);(void 0!==s||void 0!==o)&&(s||(s=0),o||(o=0),0===s||0===o||rv(s)===rv(o)?(e[a]=Math.max(eS(rg(s),rg(o),r),0),(el.test(o)||el.test(s))&&(e[a]+="%")):e[a]=o)}(t.rotate||n.rotate)&&(e.rotate=eS(t.rotate||0,n.rotate||0,r))}(a,i,this.latestValues,r,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{rn.hasAnimatedSinceResize=!0,F.layout++,this.motionValue||(this.motionValue=A(0)),this.currentAnimation=function(e,t,n){let r=O(e)?e:A(e);return r.start(t4("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{F.layout--},onComplete:()=>{F.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&ie(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nA();let t=nP(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nP(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rR(t,n),nF(t,i),nx(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rU),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rH("z",e,r,this.animationValues);for(let t=0;t<rF.length;t++)rH(`rotate${rF[t]}`,e,r,this.animationValues),rH(`skew${rF[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let n=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=rp(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rp(t?.pointerEvents)||""),this.hasProjected&&!nD(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1);return}e.visibility="";let i=r.animationValues||r.latestValues;this.applyTransformsToTarget();let a=function(e,t,n){let r="",i=e.x.translate/t.x,a=e.y.translate/t.y,s=n?.z||0;if((i||a||s)&&(r=`translate3d(${i}px, ${a}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:a,skewX:s,skewY:o}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),a&&(r+=`rotateY(${a}deg) `),s&&(r+=`skewX(${s}deg) `),o&&(r+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(r+=`scale(${o}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);n&&(a=n(i,a)),e.transform=a;let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,r.animationValues?e.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:e.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ra){if(void 0===i[t])continue;let{correct:n,applyTo:s,isCSSVariable:o}=ra[t],l="none"===a?i[t]:n(i[t],r);if(s){let t=s.length;for(let n=0;n<t;n++)e[s[n]]=l}else o?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=r===this?rp(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rG),this.root.sharedNodes.clear()}}}function rK(e){e.updateLayout()}function rz(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,a=t.source!==e.layout.source;"size"===i?nj(e=>{let r=a?t.measuredBox[e]:t.layoutBox[e],i=nP(r);r.min=n[e].min,r.max=r.min+i}):ie(i,t.layoutBox,n)&&nj(r=>{let i=a?t.measuredBox[r]:t.layoutBox[r],s=nP(n[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=n_();nx(s,n,t.layoutBox);let o=n_();a?nx(o,e.applyTransform(r,!0),t.measuredBox):nx(o,n,t.layoutBox);let l=!rC(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:a}=r;if(i&&a){let s=nA();nE(s,t.layoutBox,i.layoutBox);let o=nA();nE(o,n,a.layoutBox),rk(s,o)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:o,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function r$(e){d.value&&rI.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rY(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rX(e){e.clearSnapshot()}function rG(e){e.clearMeasurements()}function rq(e){e.isLayoutDirty=!1}function rZ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rQ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rJ(e){e.resolveTargetDelta()}function r0(e){e.calcProjection()}function r1(e){e.resetSkewAndRotation()}function r2(e){e.removeLeadSnapshot()}function r3(e,t,n){e.translate=eS(t.translate,0,n),e.scale=eS(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function r5(e,t,n,r){e.min=eS(t.min,n.min,r),e.max=eS(t.max,n.max,r)}function r9(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r4={duration:.45,ease:[.4,0,.1,1]},r6=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r7=r6("applewebkit/")&&!r6("chrome/")?Math.round:u;function r8(e){e.min=r7(e.min),e.max=r7(e.max)}function ie(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rV(t)-rV(n)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=rW({attachResizeListener:(e,t)=>np(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},ia=rW({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function is(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function io(e){return!("touch"===e.pointerType||nf.x||nf.y)}function il(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&p.postRender(()=>i(t,ny(t)))}class iu extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,a]=is(e,n),s=e=>{if(!io(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let a=e=>{io(e)&&(r(e),n.removeEventListener("pointerleave",a))};n.addEventListener("pointerleave",a,i)};return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),a}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class ic extends nu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=V(np(this.node.current,"focus",()=>this.onFocus()),np(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ih=(e,t)=>!!t&&(e===t||ih(e,t.parentElement)),id=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function iy(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let ig=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=im(()=>{if(ip.has(n))return;iy(n,"down");let e=im(()=>{iy(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>iy(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function iv(e){return nm(e)&&!(nf.x||nf.y)}function ib(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&p.postRender(()=>i(t,ny(t)))}class iP extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,a]=is(e,n),s=e=>{let r=e.currentTarget;if(!iv(e))return;ip.add(r);let a=t(r,e),s=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),ip.has(r)&&ip.delete(r),iv(e)&&"function"==typeof a&&a(e,{success:t})},o=e=>{s(e,r===window||r===document||n.useGlobalTarget||ih(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",o,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),(0,tq.s)(e))&&(e.addEventListener("focus",e=>ig(e,i)),id.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),a}(e,(e,t)=>(ib(this.node,t,"Start"),(e,{success:t})=>ib(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iT=new WeakMap,ix=new WeakMap,iw=e=>{let t=iT.get(e.target);t&&t(e)},iR=e=>{e.forEach(iw)},iE={some:0,all:1};class iS extends nu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,a={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:iE[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;ix.has(n)||ix.set(n,{});let r=ix.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iR,{root:e,...t})),r[i]}(t);return iT.set(e,n),r.observe(e),()=>{iT.delete(e),r.unobserve(e)}}(this.node.current,a,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),a=t?n:r;a&&a(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let i_=(0,n7.createContext)({strict:!1});var iM=n(32582);let iA=(0,n7.createContext)({});function ij(e){return i(e.animate)||nr.some(t=>nt(e[t]))}function iO(e){return!!(ij(e)||e.variants)}function iC(e){return Array.isArray(e)?e.join(" "):e}var iD=n(7044);let iL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ik={};for(let e in iL)ik[e]={isEnabled:t=>iL[e].some(e=>!!t[e])};let iV=Symbol.for("motionComponentSymbol");var iN=n(21279),iU=n(15124);function iI(e,{layout:t,layoutId:n}){return b.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ra[e]||"opacity"===e)}let iF=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iB={...X,transform:Math.round},iH={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:eo,rotateX:eo,rotateY:eo,rotateZ:eo,scale:q,scaleX:q,scaleY:q,scaleZ:q,skew:eo,skewX:eo,skewY:eo,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:G,originX:ed,originY:ed,originZ:eu,zIndex:iB,fillOpacity:G,strokeOpacity:G,numOctaves:iB},iW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iK=v.length;function iz(e,t,n){let{style:r,vars:i,transformOrigin:a}=e,s=!1,o=!1;for(let e in t){let n=t[e];if(b.has(e)){s=!0;continue}if(K(e)){i[e]=n;continue}{let t=iF(n,iH[e]);e.startsWith("origin")?(o=!0,a[e]=t):r[e]=t}}if(!t.transform&&(s||n?r.transform=function(e,t,n){let r="",i=!0;for(let a=0;a<iK;a++){let s=v[a],o=e[s];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!s.startsWith("scale"):0===parseFloat(o))||n){let e=iF(o,iH[s]);if(!l){i=!1;let t=iW[s]||s;r+=`${t}(${e}) `}n&&(t[s]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:n=0}=a;r.transformOrigin=`${e} ${t} ${n}`}}let i$=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iY(e,t,n){for(let r in t)O(t[r])||iI(r,n)||(e[r]=t[r])}let iX={offset:"stroke-dashoffset",array:"stroke-dasharray"},iG={offset:"strokeDashoffset",array:"strokeDasharray"};function iq(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:a=1,pathOffset:s=0,...o},l,u,c){if(iz(e,o,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:d}=e;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==t&&(h.x=t),void 0!==n&&(h.y=n),void 0!==r&&(h.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let a=i?iX:iG;e[a.offset]=eu.transform(-r);let s=eu.transform(t),o=eu.transform(n);e[a.array]=`${s} ${o}`}(h,i,a,s,!1)}let iZ=()=>({...i$(),attrs:{}}),iQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iJ.has(e)}let i1=e=>!i0(e);try{!function(e){"function"==typeof e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i3(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i5=n(72789);let i9=e=>(t,n)=>{let r=(0,n7.useContext)(iA),a=(0,n7.useContext)(iN.t),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,a){return{latestValues:function(e,t,n,r){let a={},o=r(e,{});for(let e in o)a[e]=rp(o[e]);let{initial:l,animate:u}=e,c=ij(e),h=iO(e);t&&h&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let d=!!n&&!1===n.initial,f=(d=d||!1===l)?u:l;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let r=s(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(a[e]=t)}for(let t in e)a[t]=e[t]}}}return a}(n,r,a,e),renderState:t()}})(e,t,r,a);return n?o():(0,i5.M)(o)};function i4(e,t,n){let{style:r}=e,i={};for(let a in r)(O(r[a])||t.style&&O(t.style[a])||iI(a,e)||n?.getValue(a)?.liveStyle!==void 0)&&(i[a]=r[a]);return i}let i6={useVisualState:i9({scrapeMotionValuesFromProps:i4,createRenderState:i$})};function i7(e,t,n){let r=i4(e,t,n);for(let n in e)(O(e[n])||O(t[n]))&&(r[-1!==v.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i8={useVisualState:i9({scrapeMotionValuesFromProps:i7,createRenderState:iZ})},ae=e=>t=>t.test(e),at=[X,eu,el,eo,eh,ec,{test:e=>"auto"===e,parse:e=>e}],an=e=>at.find(ae(e)),ar=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),ai=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,aa=e=>/^0[^.\s]+$/u.test(e),as=new Set(["brightness","contrast","saturate","opacity"]);function ao(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(Q)||[];if(!r)return e;let i=n.replace(r,""),a=+!!as.has(t);return r!==n&&(a*=100),t+"("+a+i+")"}let al=/\b([a-z-]*)\(.*?\)/gu,au={...ew,getAnimatableNone:e=>{let t=e.match(al);return t?t.map(ao).join(" "):e}},ac={...iH,color:ep,backgroundColor:ep,outlineColor:ep,fill:ep,stroke:ep,borderColor:ep,borderTopColor:ep,borderRightColor:ep,borderBottomColor:ep,borderLeftColor:ep,filter:au,WebkitFilter:au},ah=e=>ac[e];function ad(e,t){let n=ah(e);return n!==au&&(n=ew),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let af=new Set(["auto","none","0"]);class ap extends tk{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&$(r=r.trim())){let i=function e(t,n,r=1){H(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,a]=function(e){let t=ai.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return ar(e)?parseFloat(e):e}return $(a)?e(a,n,r+1):a}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!P.has(n)||2!==e.length)return;let[r,i]=e,a=an(r),s=an(i);if(a!==s)if(tE(a)&&tE(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tM[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||aa(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!af.has(t)&&eb(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=ad(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tM[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,a=n[i];n[i]=tM[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let am=[...at,ep,ew],ay=e=>am.find(ae(e)),ag={current:null},av={current:!1},ab=new WeakMap,aP=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class aT{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:a},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tk,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=E.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,p.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=a;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=ij(t),this.isVariantNode=iO(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==o[e]&&O(t)&&t.set(o[e],!1)}}mount(e){this.current=e,ab.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),av.current||function(){if(av.current=!0,iD.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ag.current=e.matches;e.addEventListener("change",t),t()}else ag.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ag.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=b.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&p.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),a=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),a(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in ik){let t=ik[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nA()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<aP.length;t++){let n=aP[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],a=n[r];if(O(i))e.addValue(r,i);else if(O(a))e.addValue(r,A(i,{owner:e}));else if(a!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,A(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=A(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(ar(n)||aa(n))?n=parseFloat(n):!ay(n)&&ew.test(t)&&(n=ad(e,t)),this.setBaseTarget(e,O(n)?n.get():n)),O(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=s(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||O(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new w),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class ax extends aT{constructor(){super(...arguments),this.KeyframeResolver=ap}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;O(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function aw(e,{style:t,vars:n},r,i){let a,s=e.style;for(a in t)s[a]=t[a];for(a in i?.applyProjectionStyles(s,r),n)s.setProperty(a,n[a])}class aR extends ax{constructor(){super(...arguments),this.type="html",this.renderInstance=aw}readValueFromInstance(e,t){if(b.has(t))return this.projection?.isProjecting?tT(t):tw(e,t);{let n=window.getComputedStyle(e),r=(K(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nB(e,t)}build(e,t,n){iz(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i4(e,t,n)}}let aE=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class aS extends ax{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nA}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(b.has(t)){let e=ah(t);return e&&e.default||0}return t=aE.has(t)?t:D(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i7(e,t,n)}build(e,t,n){iq(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in aw(e,t,void 0,r),t.attrs)e.setAttribute(aE.has(n)?n:D(n),t.attrs[n])}mount(e){this.isSVGTag=iQ(e.tagName),super.mount(e)}}let a_=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((tX={animation:{Feature:nc},exit:{Feature:nd},inView:{Feature:iS},tap:{Feature:iP},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:n9},drag:{Feature:n3,ProjectionNode:ia,MeasureLayout:rl},layout:{ProjectionNode:ia,MeasureLayout:rl}},tG=(e,t)=>i3(e)?new aS(t):new aR(t,{allowProjection:e!==n7.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){function a(e,a){var s,o,l;let u,c={...(0,n7.useContext)(iM.Q),...e,layoutId:function({layoutId:e}){let t=(0,n7.useContext)(re.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:h}=c,d=function(e){let{initial:t,animate:n}=function(e,t){if(ij(e)){let{initial:t,animate:n}=e;return{initial:!1===t||nt(t)?t:void 0,animate:nt(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,n7.useContext)(iA));return(0,n7.useMemo)(()=>({initial:t,animate:n}),[iC(t),iC(n)])}(e),f=r(e,h);if(!h&&iD.B){o=0,l=0,(0,n7.useContext)(i_).strict;let e=function(e){let{drag:t,layout:n}=ik;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);u=e.MeasureLayout,d.visualElement=function(e,t,n,r,i){let{visualElement:a}=(0,n7.useContext)(iA),s=(0,n7.useContext)(i_),o=(0,n7.useContext)(iN.t),l=(0,n7.useContext)(iM.Q).reducedMotion,u=(0,n7.useRef)(null);r=r||s.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:a,props:n,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let c=u.current,h=(0,n7.useContext)(rt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,n,r){let{layoutId:i,layout:a,drag:s,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:a,alwaysMeasureLayout:!!s||o&&nW(o),visualElement:e,animationType:"string"==typeof a?a:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,n,i,h);let d=(0,n7.useRef)(!1);(0,n7.useInsertionEffect)(()=>{c&&d.current&&c.update(n,o)});let f=n[L],p=(0,n7.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,iU.E)(()=>{c&&(d.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),n6.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),(0,n7.useEffect)(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),p.current=!1))}),c}(i,f,c,t,e.ProjectionNode)}return(0,n4.jsxs)(iA.Provider,{value:d,children:[u&&d.visualElement?(0,n4.jsx)(u,{visualElement:d.visualElement,...c}):null,n(i,e,(s=d.visualElement,(0,n7.useCallback)(e=>{e&&f.onMount&&f.onMount(e),s&&(e?s.mount(e):s.unmount()),a&&("function"==typeof a?a(e):nW(a)&&(a.current=e))},[s])),f,h,d.visualElement)]})}e&&function(e){for(let t in e)ik[t]={...ik[t],...e[t]}}(e),a.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let s=(0,n7.forwardRef)(a);return s[iV]=i,s}({...i3(e)?i8:i6,preloadedFeatures:tX,useRender:function(e=!1){return(t,n,r,{latestValues:i},a)=>{let s=(i3(t)?function(e,t,n,r){let i=(0,n7.useMemo)(()=>{let n=iZ();return iq(n,t,iQ(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iY(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iY(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,n7.useMemo)(()=>{let n=i$();return iz(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,a,t),o=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===n&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==n7.Fragment?{...o,...s,ref:r}:{},{children:u}=n,c=(0,n7.useMemo)(()=>O(u)?u.get():u,[u]);return(0,n7.createElement)(t,{...l,children:c})}}(t),createVisualElement:tG,Component:e})}))},26736:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let r=n(2255);function i(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let r=n(57391),i=n(70642);function a(e,t){var n;let{url:a,tree:s}=t,o=(0,r.createHrefFromUrl)(a),l=s||e.tree,u=e.cache;return{canonicalUrl:o,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(n=(0,i.extractPathFromFlightRouterState)(l))?n:a.pathname}}n(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let r=n(57391),i=n(86770),a=n(2030),s=n(25232),o=n(56928),l=n(59435),u=n(89752);function c(e,t){let{serverResponse:{flightData:n,canonicalUrl:c},navigatedAt:h}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof n)return(0,s.handleExternalUrl)(e,d,n,e.pushRef.pendingPush);let f=e.tree,p=e.cache;for(let t of n){let{segmentPath:n,tree:l}=t,m=(0,i.applyRouterStatePatchToTree)(["",...n],f,l,e.canonicalUrl);if(null===m)return e;if((0,a.isNavigatingToNewRootLayout)(f,m))return(0,s.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let y=c?(0,r.createHrefFromUrl)(c):void 0;y&&(d.canonicalUrl=y);let g=(0,u.createEmptyCacheNode)();(0,o.applyFlightData)(h,p,g,t),d.patchedTree=m,d.cache=g,p=g,f=m}return(0,l.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return o},urlObjectKeys:function(){return s}});let r=n(40740)._(n(76715)),i=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",s=e.pathname||"",o=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||i.test(a))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),o&&"#"!==o[0]&&(o="#"+o),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+o}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return a(e)}},32582:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(43210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},32708:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},33898:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let r=n(34400),i=n(41500),a=n(33123),s=n(83913);function o(e,t,n,o,l,u){let{segmentPath:c,seedData:h,tree:d,head:f}=o,p=t,m=n;for(let t=0;t<c.length;t+=2){let n=c[t],o=c[t+1],y=t===c.length-2,g=(0,a.createRouterCacheKey)(o),v=m.parallelRoutes.get(n);if(!v)continue;let b=p.parallelRoutes.get(n);b&&b!==v||(b=new Map(v),p.parallelRoutes.set(n,b));let P=v.get(g),T=b.get(g);if(y){if(h&&(!T||!T.lazyData||T===P)){let t=h[0],n=h[1],a=h[3];T={lazyData:null,rsc:u||t!==s.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:u&&P?new Map(P.parallelRoutes):new Map,navigatedAt:e},P&&u&&(0,r.invalidateCacheByRouterState)(T,P,d),u&&(0,i.fillLazyItemsTillLeafWithHead)(e,T,P,d,h,f,l),b.set(g,T)}continue}T&&P&&(T===P&&(T={lazyData:T.lazyData,rsc:T.rsc,prefetchRsc:T.prefetchRsc,head:T.head,prefetchHead:T.prefetchHead,parallelRoutes:new Map(T.parallelRoutes),loading:T.loading},b.set(g,T)),p=T,m=P)}}function l(e,t,n,r,i){o(e,t,n,r,i,!0)}function u(e,t,n,r,i){o(e,t,n,r,i,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let r=n(33123);function i(e,t,n){for(let i in n[1]){let a=n[1][i][0],s=(0,r.createRouterCacheKey)(a),o=t.parallelRoutes.get(i);if(o){let t=new Map(o);t.delete(s),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35416:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return l},isBot:function(){return o}});let r=n(95796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=r.HTML_LIMITED_BOT_UA_RE.source;function s(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function o(e){return i.test(e)||s(e)}function l(e){return i.test(e)?"dom":s(e)?"html":void 0}},35429:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return A}});let r=n(11264),i=n(11448),a=n(91563),s=n(59154),o=n(6361),l=n(57391),u=n(25232),c=n(86770),h=n(2030),d=n(59435),f=n(41500),p=n(89752),m=n(68214),y=n(96493),g=n(22308),v=n(74007),b=n(36875),P=n(97860),T=n(5334),x=n(25942),w=n(26736),R=n(24642);n(50593);let{createFromFetch:E,createTemporaryReferenceSet:S,encodeReply:_}=n(19357);async function M(e,t,n){let s,l,{actionId:u,actionArgs:c}=n,h=S(),d=(0,R.extractInfoFromServerReferenceId)(u),f="use-cache"===d.type?(0,R.omitUnusedArgs)(c,d):c,p=await _(f,{temporaryReferences:h}),m=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:u,[a.NEXT_ROUTER_STATE_TREE_HEADER]:(0,v.prepareFlightRouterStateForRequest)(e.tree),...{},...t?{[a.NEXT_URL]:t}:{}},body:p}),y=m.headers.get("x-action-redirect"),[g,b]=(null==y?void 0:y.split(";"))||[];switch(b){case"push":s=P.RedirectType.push;break;case"replace":s=P.RedirectType.replace;break;default:s=void 0}let T=!!m.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let x=g?(0,o.assignLocation)(g,new URL(e.canonicalUrl,window.location.href)):void 0,w=m.headers.get("content-type");if(null==w?void 0:w.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await E(Promise.resolve(m),{callServer:r.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:h});return g?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:x,redirectType:s,revalidatedParts:l,isPrerender:T}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:x,redirectType:s,revalidatedParts:l,isPrerender:T}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===w?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:x,redirectType:s,revalidatedParts:l,isPrerender:T}}function A(e,t){let{resolve:n,reject:r}=t,i={},a=e.tree;i.preserveCustomHistoryState=!1;let o=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return M(e,o,t).then(async m=>{let R,{actionResult:E,actionFlightData:S,redirectLocation:_,redirectType:M,isPrerender:A,revalidatedParts:j}=m;if(_&&(M===P.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=R=(0,l.createHrefFromUrl)(_,!1)),!S)return(n(E),_)?(0,u.handleExternalUrl)(e,i,_.href,e.pushRef.pendingPush):e;if("string"==typeof S)return n(E),(0,u.handleExternalUrl)(e,i,S,e.pushRef.pendingPush);let O=j.paths.length>0||j.tag||j.cookie;for(let r of S){let{tree:s,seedData:l,head:d,isRootRender:m}=r;if(!m)return console.log("SERVER ACTION APPLY FAILED"),n(E),e;let b=(0,c.applyRouterStatePatchToTree)([""],a,s,R||e.canonicalUrl);if(null===b)return n(E),(0,y.handleSegmentMismatch)(e,t,s);if((0,h.isNavigatingToNewRootLayout)(a,b))return n(E),(0,u.handleExternalUrl)(e,i,R||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],n=(0,p.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=l[3],(0,f.fillLazyItemsTillLeafWithHead)(v,n,void 0,s,l,d,void 0),i.cache=n,i.prefetchCache=new Map,O&&await (0,g.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:b,updatedCache:n,includeNextUrl:!!o,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=b,a=b}return _&&R?(O||((0,T.createSeededPrefetchCacheEntry)({url:_,data:{flightData:S,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:A?s.PrefetchKind.FULL:s.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),r((0,b.getRedirectError)((0,w.hasBasePath)(R)?(0,x.removeBasePath)(R):R,M||P.RedirectType.push))):n(E),(0,d.handleMutable)(e,i)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41500:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,a,s,o,l,u){if(0===Object.keys(s[1]).length){n.head=l;return}for(let c in s[1]){let h,d=s[1][c],f=d[0],p=(0,r.createRouterCacheKey)(f),m=null!==o&&void 0!==o[2][c]?o[2][c]:null;if(a){let r=a.parallelRoutes.get(c);if(r){let a,s=(null==u?void 0:u.kind)==="auto"&&u.status===i.PrefetchCacheEntryStatus.reusable,o=new Map(r),h=o.get(p);a=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==h?void 0:h.parallelRoutes),navigatedAt:t}:s&&h?{lazyData:h.lazyData,rsc:h.rsc,prefetchRsc:h.prefetchRsc,head:h.head,prefetchHead:h.prefetchHead,parallelRoutes:new Map(h.parallelRoutes),loading:h.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==h?void 0:h.parallelRoutes),loading:null,navigatedAt:t},o.set(p,a),e(t,a,h,d,m||null,l,u),n.parallelRoutes.set(c,o);continue}}if(null!==m){let e=m[1],n=m[3];h={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else h={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let y=n.parallelRoutes.get(c);y?y.set(p,h):n.parallelRoutes.set(c,new Map([[p,h]])),e(t,h,void 0,d,m,l,u)}}}});let r=n(33123),i=n(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44397:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let r=n(33123);function i(e,t){return function e(t,n,i){if(0===Object.keys(n).length)return[t,i];let a=Object.keys(n).filter(e=>"children"!==e);for(let s of("children"in n&&a.unshift("children"),a)){let[a,o]=n[s],l=t.parallelRoutes.get(s);if(!l)continue;let u=(0,r.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let h=e(c,o,i+"/"+u);if(h)return h}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},50593:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return h},PrefetchPriority:function(){return d},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return s},navigate:function(){return i},prefetch:function(){return r},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return o}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,i=n,a=n,s=n,o=n,l=n,u=n,c=n;var h=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),d=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,n)=>{function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},53038:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(43210);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=a(e,r)),t&&(i.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54674:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let r=n(84949),i=n(19169),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:a}=(0,i.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56928:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let r=n(41500),i=n(33898);function a(e,t,n,a,s){let{tree:o,seedData:l,head:u,isRootRender:c}=a;if(null===l)return!1;if(c){let i=l[1];n.loading=l[3],n.rsc=i,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,o,l,u,s)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,i.fillCacheWithNewSubTreeData)(e,n,t,a,s);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59435:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let r=n(70642);function i(e){return void 0!==e}function a(e,t){var n,a;let s=null==(n=t.shouldScroll)||n,o=e.nextUrl;if(i(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?o=n:o||(o=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!s&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:s?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:s?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:o}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,n)=>{n.r(t),n.d(t,{_:()=>i});var r=0;function i(e){return"__private_"+r+++"_"+e}},61794:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let r=n(79289),i=n(26736);function a(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},62688:(e,t,n)=>{n.d(t,{A:()=>h});var r=n(43210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),s=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:a="",children:s,iconNode:c,...h},d)=>(0,r.createElement)("svg",{ref:d,...u,width:t,height:t,stroke:e,strokeWidth:i?24*Number(n)/Number(t):n,className:o("lucide",a),...!s&&!l(h)&&{"aria-hidden":"true"},...h},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(s)?s:[s]])),h=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...a},l)=>(0,r.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${i(s(e))}`,`lucide-${e}`,n),...a}));return n.displayName=s(e),n}},63690:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return p},dispatchNavigateAction:function(){return g},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return b}});let r=n(59154),i=n(8830),a=n(43210),s=n(91992);n(50593);let o=n(19129),l=n(96127),u=n(89752),c=n(75076),h=n(73406);function d(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?f({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function f(e){let{actionQueue:t,action:n,setState:r}=e,i=t.state;t.pending=n;let a=n.payload,o=t.action(i,a);function l(e){n.discarded||(t.state=e,d(t,r),n.resolve(e))}(0,s.isThenable)(o)?o.then(l,e=>{d(t,r),n.reject(e)}):l(o)}function p(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let i={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,a.startTransition)(()=>{n(e)})}let s={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=s,f({actionQueue:e,action:s,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,s.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),f({actionQueue:e,action:s,setState:n})):(null!==e.last&&(e.last.next=s),e.last=s)})(n,e,t),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function m(){return null}function y(){return null}function g(e,t,n,i){let a=new URL((0,l.addBasePath)(e),location.href);(0,h.setLinkForCurrentNavigation)(i);(0,o.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:a,isExternalUrl:(0,u.isExternalURL)(a),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function v(e,t){(0,o.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),i=(0,u.createPrefetchURL)(e);if(null!==i){var a;(0,c.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:i,kind:null!=(a=null==t?void 0:t.kind)?a:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var n;g(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var n;g(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,o.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65951:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[a,s]=n,[o,l]=t;return(0,i.matchSegment)(o,a)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),s[l]):!!Array.isArray(o)}}});let r=n(74007),i=n(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return f},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],i=t.parallelRoutes,s=new Map(i);for(let t in r){let n=r[t],o=n[0],l=(0,a.createRouterCacheKey)(o),u=i.get(t);if(void 0!==u){let r=u.get(l);if(void 0!==r){let i=e(r,n),a=new Map(u);a.set(l,i),s.set(t,a)}}}let o=t.rsc,l=g(o)&&"pending"===o.status;return{lazyData:null,rsc:o,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:s,navigatedAt:t.navigatedAt}}}});let r=n(83913),i=n(14077),a=n(33123),s=n(2030),o=n(5334),l={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,n,s,o,u,d,f,p){return function e(t,n,s,o,u,d,f,p,m,y,g){let v=s[1],b=o[1],P=null!==d?d[2]:null;u||!0===o[4]&&(u=!0);let T=n.parallelRoutes,x=new Map(T),w={},R=null,E=!1,S={};for(let n in b){let s,o=b[n],h=v[n],d=T.get(n),_=null!==P?P[n]:null,M=o[0],A=y.concat([n,M]),j=(0,a.createRouterCacheKey)(M),O=void 0!==h?h[0]:void 0,C=void 0!==d?d.get(j):void 0;if(null!==(s=M===r.DEFAULT_SEGMENT_KEY?void 0!==h?{route:h,node:null,dynamicRequestTree:null,children:null}:c(t,h,o,C,u,void 0!==_?_:null,f,p,A,g):m&&0===Object.keys(o[1]).length?c(t,h,o,C,u,void 0!==_?_:null,f,p,A,g):void 0!==h&&void 0!==O&&(0,i.matchSegment)(M,O)&&void 0!==C&&void 0!==h?e(t,C,h,o,u,_,f,p,m,A,g):c(t,h,o,C,u,void 0!==_?_:null,f,p,A,g))){if(null===s.route)return l;null===R&&(R=new Map),R.set(n,s);let e=s.node;if(null!==e){let t=new Map(d);t.set(j,e),x.set(n,t)}let t=s.route;w[n]=t;let r=s.dynamicRequestTree;null!==r?(E=!0,S[n]=r):S[n]=t}else w[n]=o,S[n]=o}if(null===R)return null;let _={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:x,navigatedAt:t};return{route:h(o,w),node:_,dynamicRequestTree:E?h(o,S):null,children:R}}(e,t,n,s,!1,o,u,d,f,[],p)}function c(e,t,n,r,i,u,c,f,p,m){return!i&&(void 0===t||(0,s.isNavigatingToNewRootLayout)(t,n))?l:function e(t,n,r,i,s,l,u,c){let f,p,m,y,g=n[1],v=0===Object.keys(g).length;if(void 0!==r&&r.navigatedAt+o.DYNAMIC_STALETIME_MS>t)f=r.rsc,p=r.loading,m=r.head,y=r.navigatedAt;else if(null===i)return d(t,n,null,s,l,u,c);else if(f=i[1],p=i[3],m=v?s:null,y=t,i[4]||l&&v)return d(t,n,i,s,l,u,c);let b=null!==i?i[2]:null,P=new Map,T=void 0!==r?r.parallelRoutes:null,x=new Map(T),w={},R=!1;if(v)c.push(u);else for(let n in g){let r=g[n],i=null!==b?b[n]:null,o=null!==T?T.get(n):void 0,h=r[0],d=u.concat([n,h]),f=(0,a.createRouterCacheKey)(h),p=e(t,r,void 0!==o?o.get(f):void 0,i,s,l,d,c);P.set(n,p);let m=p.dynamicRequestTree;null!==m?(R=!0,w[n]=m):w[n]=r;let y=p.node;if(null!==y){let e=new Map;e.set(f,y),x.set(n,e)}}return{route:n,node:{lazyData:null,rsc:f,prefetchRsc:null,head:m,prefetchHead:null,loading:p,parallelRoutes:x,navigatedAt:y},dynamicRequestTree:R?h(n,w):null,children:P}}(e,n,r,u,c,f,p,m)}function h(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function d(e,t,n,r,i,s,o){let l=h(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,n,r,i,s,o,l){let u=n[1],c=null!==r?r[2]:null,h=new Map;for(let n in u){let r=u[n],d=null!==c?c[n]:null,f=r[0],p=o.concat([n,f]),m=(0,a.createRouterCacheKey)(f),y=e(t,r,void 0===d?null:d,i,s,p,l),g=new Map;g.set(m,y),h.set(n,g)}let d=0===h.size;d&&l.push(o);let f=null!==r?r[1]:null,p=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:h,prefetchRsc:void 0!==f?f:null,prefetchHead:d?i:[null,null],loading:void 0!==p?p:null,rsc:v(),head:d?v():null,navigatedAt:t}}(e,t,n,r,i,s,o),dynamicRequestTree:l,children:null}}function f(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:s,head:o}=t;s&&function(e,t,n,r,s){let o=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],a=o.children;if(null!==a){let e=a.get(n);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(r,t)){o=e;continue}}}return}!function e(t,n,r,s){if(null===t.dynamicRequestTree)return;let o=t.children,l=t.node;if(null===o){null!==l&&(function e(t,n,r,s,o){let l=n[1],u=r[1],c=s[2],h=t.parallelRoutes;for(let t in l){let n=l[t],r=u[t],s=c[t],d=h.get(t),f=n[0],p=(0,a.createRouterCacheKey)(f),y=void 0!==d?d.get(p):void 0;void 0!==y&&(void 0!==r&&(0,i.matchSegment)(f,r[0])&&null!=s?e(y,n,r,s,o):m(n,y,null))}let d=t.rsc,f=s[1];null===d?t.rsc=f:g(d)&&d.resolve(f);let p=t.head;g(p)&&p.resolve(o)}(l,t.route,n,r,s),t.dynamicRequestTree=null);return}let u=n[1],c=r[2];for(let t in n){let n=u[t],r=c[t],a=o.get(t);if(void 0!==a){let t=a.route[0];if((0,i.matchSegment)(n[0],t)&&null!=r)return e(a,n,r,s)}}}(o,n,r,s)}(e,n,r,s,o)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)m(e.route,n,t);else for(let e of r.values())p(e,t);e.dynamicRequestTree=null}function m(e,t,n){let r=e[1],i=t.parallelRoutes;for(let e in r){let t=r[e],s=i.get(e);if(void 0===s)continue;let o=t[0],l=(0,a.createRouterCacheKey)(o),u=s.get(l);void 0!==u&&m(t,u,n)}let s=t.rsc;g(s)&&(null===n?s.resolve(null):s.reject(n));let o=t.head;g(o)&&o.resolve(null)}let y=Symbol();function g(e){return e&&e.tag===y}function v(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=y,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70642:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],a=Array.isArray(t),s=a?t[1]:t;!s||s.startsWith(i.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):a&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(72859),i=n(83913),a=n(14077),s=e=>"/"===e[0]?e.slice(1):e,o=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=s(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===i.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(i.PAGE_SEGMENT_KEY))return"";let a=[o(n)],s=null!=(t=e[1])?t:{},c=s.children?u(s.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(s)){if("children"===e)continue;let n=u(t);void 0!==n&&a.push(n)}return l(a)}function c(e,t){let n=function e(t,n){let[i,s]=t,[l,c]=n,h=o(i),d=o(l);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>h.startsWith(e)||d.startsWith(e)))return"";if(!(0,a.matchSegment)(i,l)){var f;return null!=(f=u(n))?f:""}for(let t in s)if(c[t]){let n=e(s[t],c[t]);if(null!==n)return o(l)+"/"+n}return null}(e,t);return null==n||"/"===n?n:l(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72789:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(43210);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},73406:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return v},mountLinkInstance:function(){return g},onLinkVisibilityChanged:function(){return P},onNavigationIntent:function(){return T},pingVisibleLinks:function(){return w},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return h},unmountPrefetchableInstance:function(){return b}}),n(63690);let r=n(89752),i=n(59154),a=n(50593),s=n(43210),o=null,l={pending:!0},u={pending:!1};function c(e){(0,s.startTransition)(()=>{null==o||o.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(l),o=e})}function h(e){o===e&&(o=null)}let d="function"==typeof WeakMap?new WeakMap:new Map,f=new Set,p="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;P(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==d.get(e)&&b(e),d.set(e,t),null!==p&&p.observe(e)}function y(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function g(e,t,n,r,i,a){if(i){let i=y(t);if(null!==i){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:a};return m(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function v(e,t,n,r){let i=y(t);null!==i&&m(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:null})}function b(e){let t=d.get(e);if(void 0!==t){d.delete(e),f.delete(t);let n=t.prefetchTask;null!==n&&(0,a.cancelPrefetchTask)(n)}null!==p&&p.unobserve(e)}function P(e,t){let n=d.get(e);void 0!==n&&(n.isVisible=t,t?f.add(n):f.delete(n),x(n))}function T(e,t){let n=d.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,x(n))}function x(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function w(e,t){let n=(0,a.getCurrentCacheVersion)();for(let r of f){let s=r.prefetchTask;if(null!==s&&r.cacheVersion===n&&s.key.nextUrl===e&&s.treeAtTimeOfPrefetch===t)continue;null!==s&&(0,a.cancelPrefetchTask)(s);let o=(0,a.createCacheKey)(r.prefetchHref,e),l=r.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;r.prefetchTask=(0,a.schedulePrefetchTask)(o,t,r.kind===i.PrefetchKind.FULL,l),r.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74479:(e,t,n)=>{n.d(t,{G:()=>r});function r(e){return"object"==typeof e&&null!==e}},75076:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return s}});let r=n(5144),i=n(5334),a=new r.PromiseQueue(5),s=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},77022:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return s}});let r=n(43210),i=n(51215),a="next-route-announcer";function s(e){let{tree:t}=e,[n,s]=(0,r.useState)(null);(0,r.useEffect)(()=>(s(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[o,l]=(0,r.useState)(""),u=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),n?(0,i.createPortal)(o,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78866:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let r=n(59008),i=n(57391),a=n(86770),s=n(2030),o=n(25232),l=n(59435),u=n(41500),c=n(89752),h=n(96493),d=n(68214),f=n(22308);function p(e,t){let{origin:n}=t,p={},m=e.canonicalUrl,y=e.tree;p.preserveCustomHistoryState=!1;let g=(0,c.createEmptyCacheNode)(),v=(0,d.hasInterceptionRouteInCurrentTree)(e.tree);g.lazyData=(0,r.fetchServerResponse)(new URL(m,n),{flightRouterState:[y[0],y[1],y[2],"refetch"],nextUrl:v?e.nextUrl:null});let b=Date.now();return g.lazyData.then(async n=>{let{flightData:r,canonicalUrl:c}=n;if("string"==typeof r)return(0,o.handleExternalUrl)(e,p,r,e.pushRef.pendingPush);for(let n of(g.lazyData=null,r)){let{tree:r,seedData:l,head:d,isRootRender:P}=n;if(!P)return console.log("REFRESH FAILED"),e;let T=(0,a.applyRouterStatePatchToTree)([""],y,r,e.canonicalUrl);if(null===T)return(0,h.handleSegmentMismatch)(e,t,r);if((0,s.isNavigatingToNewRootLayout)(y,T))return(0,o.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let x=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=x),null!==l){let e=l[1],t=l[3];g.rsc=e,g.prefetchRsc=null,g.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(b,g,void 0,r,l,d,void 0),p.prefetchCache=new Map}await (0,f.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:T,updatedCache:g,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=g,p.patchedTree=T,y=T}return(0,l.handleMutable)(e,p)},()=>e)}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return y},SP:function(){return d},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,f=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},84949:(e,t)=>{function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},85814:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return y},useLinkStatus:function(){return v}});let r=n(40740),i=n(60687),a=r._(n(43210)),s=n(30195),o=n(22142),l=n(59154),u=n(53038),c=n(79289),h=n(96127);n(50148);let d=n(73406),f=n(61794),p=n(63690);function m(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function y(e){let t,n,r,[s,y]=(0,a.useOptimistic)(d.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:b,as:P,children:T,prefetch:x=null,passHref:w,replace:R,shallow:E,scroll:S,onClick:_,onMouseEnter:M,onTouchStart:A,legacyBehavior:j=!1,onNavigate:O,ref:C,unstable_dynamicOnHover:D,...L}=e;t=T,j&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let k=a.default.useContext(o.AppRouterContext),V=!1!==x,N=null===x?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:U,as:I}=a.default.useMemo(()=>{let e=m(b);return{href:e,as:P?m(P):e}},[b,P]);j&&(n=a.default.Children.only(t));let F=j?n&&"object"==typeof n&&n.ref:C,B=a.default.useCallback(e=>(null!==k&&(v.current=(0,d.mountLinkInstance)(e,U,k,N,V,y)),()=>{v.current&&((0,d.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,d.unmountPrefetchableInstance)(e)}),[V,U,k,N,y]),H={ref:(0,u.useMergedRef)(B,F),onClick(e){j||"function"!=typeof _||_(e),j&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),k&&(e.defaultPrevented||function(e,t,n,r,i,s,o){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(o){let e=!1;if(o({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(n||t,i?"replace":"push",null==s||s,r.current)})}}(e,U,I,v,R,S,O))},onMouseEnter(e){j||"function"!=typeof M||M(e),j&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),k&&V&&(0,d.onNavigationIntent)(e.currentTarget,!0===D)},onTouchStart:function(e){j||"function"!=typeof A||A(e),j&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),k&&V&&(0,d.onNavigationIntent)(e.currentTarget,!0===D)}};return(0,c.isAbsoluteUrl)(I)?H.href=I:j&&!w&&("a"!==n.type||"href"in n.props)||(H.href=(0,h.addBasePath)(I)),r=j?a.default.cloneElement(n,H):(0,i.jsx)("a",{...L,...H,children:t}),(0,i.jsx)(g.Provider,{value:s,children:r})}n(32708);let g=(0,a.createContext)(d.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86044:(e,t,n)=>{n.d(t,{xQ:()=>a});var r=n(43210),i=n(21279);function a(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:o}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return o(l)},[e]);let u=(0,r.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},86770:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,l){let u,[c,h,d,f,p]=n;if(1===t.length){let e=o(n,r);return(0,s.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,y]=t;if(!(0,a.matchSegment)(m,c))return null;if(2===t.length)u=o(h[y],r);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),h[y],r,l)))return null;let g=[t[0],{...h,[y]:u},d,f];return p&&(g[4]=!0),(0,s.addRefreshMarkerToActiveParallelSegments)(g,l),g}}});let r=n(83913),i=n(74007),a=n(14077),s=n(22308);function o(e,t){let[n,i]=e,[s,l]=t;if(s===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(n,s)){let t={};for(let e in i)void 0!==l[e]?t[e]=o(i[e],l[e]):t[e]=i[e];for(let e in l)t[e]||(t[e]=l[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return A},createPrefetchURL:function(){return _},default:function(){return D},isExternalURL:function(){return S}});let r=n(40740),i=n(60687),a=r._(n(43210)),s=n(22142),o=n(59154),l=n(57391),u=n(10449),c=n(19129),h=r._(n(35656)),d=n(35416),f=n(96127),p=n(77022),m=n(67086),y=n(44397),g=n(89330),v=n(25942),b=n(26736),P=n(70642),T=n(12776),x=n(63690),w=n(36875),R=n(97860);n(73406);let E={};function S(e){return e.origin!==window.location.origin}function _(e){let t;if((0,d.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,f.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return S(t)?null:t}function M(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,i={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(i,"",r)):window.history.replaceState(i,"",r)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function A(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function j(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function O(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,i=null!==r?r:n;return(0,a.useDeferredValue)(n,i)}function C(e){let t,{actionQueue:n,assetPrefix:r,globalError:l}=e,d=(0,c.useActionQueue)(n),{canonicalUrl:f}=d,{searchParams:T,pathname:S}=(0,a.useMemo)(()=>{let e=new URL(f,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[f]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(E.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:o.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,R.isRedirectError)(t)){e.preventDefault();let n=(0,w.getURLFromRedirectError)(t);(0,w.getRedirectTypeFromError)(t)===R.RedirectType.push?x.publicAppRouterInstance.push(n,{}):x.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:_}=d;if(_.mpaNavigation){if(E.pendingMpaPath!==f){let e=window.location;_.pendingPush?e.assign(f):e.replace(f),E.pendingMpaPath=f}(0,a.use)(g.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:o.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=j(t),i&&n(i)),e(t,r,i)},window.history.replaceState=function(e,r,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=j(e),i&&n(i)),t(e,r,i)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,x.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:A,tree:C,nextUrl:D,focusAndScrollRef:L}=d,k=(0,a.useMemo)(()=>(0,y.findHeadInCache)(A,C[1]),[A,C]),N=(0,a.useMemo)(()=>(0,P.getSelectedParams)(C),[C]),U=(0,a.useMemo)(()=>({parentTree:C,parentCacheNode:A,parentSegmentPath:null,url:f}),[C,A,f]),I=(0,a.useMemo)(()=>({tree:C,focusAndScrollRef:L,nextUrl:D}),[C,L,D]);if(null!==k){let[e,n]=k;t=(0,i.jsx)(O,{headCacheNode:e},n)}else t=null;let F=(0,i.jsxs)(m.RedirectBoundary,{children:[t,A.rsc,(0,i.jsx)(p.AppRouterAnnouncer,{tree:C})]});return F=(0,i.jsx)(h.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:F}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(M,{appRouterState:d}),(0,i.jsx)(V,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:N,children:(0,i.jsx)(u.PathnameContext.Provider,{value:S,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:T,children:(0,i.jsx)(s.GlobalLayoutRouterContext.Provider,{value:I,children:(0,i.jsx)(s.AppRouterContext.Provider,{value:x.publicAppRouterInstance,children:(0,i.jsx)(s.LayoutRouterContext.Provider,{value:U,children:F})})})})})})]})}function D(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:a}=e;return(0,T.useNavFailureHandler)(),(0,i.jsx)(h.ErrorBoundary,{errorComponent:h.default,children:(0,i.jsx)(C,{actionQueue:t,assetPrefix:a,globalError:[n,r]})})}let L=new Set,k=new Set;function V(){let[,e]=a.default.useState(0),t=L.size;return(0,a.useEffect)(()=>{let n=()=>e(e=>e+1);return k.add(n),t!==L.size&&n(),()=>{k.delete(n)}},[t,e]),[...L].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=L.size;return L.add(e),L.size!==t&&k.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95796:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let r=n(98834),i=n(54674);function a(e,t){return(0,i.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96493:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let r=n(25232);function i(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let s=a.length<=2,[o,l]=a,u=(0,i.createRouterCacheKey)(l),c=n.parallelRoutes.get(o),h=t.parallelRoutes.get(o);h&&h!==c||(h=new Map(c),t.parallelRoutes.set(o,h));let d=null==c?void 0:c.get(u),f=h.get(u);if(s){f&&f.lazyData&&f!==d||h.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!f||!d){f||h.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading},h.set(u,f)),e(f,d,(0,r.getNextFlightSegmentPath)(a))}}});let r=n(74007),i=n(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(59008),n(57391),n(86770),n(2030),n(25232),n(59435),n(56928),n(89752),n(96493),n(68214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let r=n(19169);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:i,hash:a}=(0,r.parsePath)(e);return""+t+n+i+a}}};