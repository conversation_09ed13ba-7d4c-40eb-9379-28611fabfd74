(()=>{var e={};e.id=856,e.ids=[856],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16957:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>l,serverHooks:()=>N,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{DELETE:()=>d,GET:()=>p,PUT:()=>c});var o=r(96559),a=r(48088),n=r(37719),i=r(32190),u=r(56621);async function p(e,{params:t}){try{let{id:e}=await t,{data:r,error:s}=await u.N.from("products").select("*").eq("id",e).single();if(s)return i.NextResponse.json({error:s.message},{status:500});if(!r)return i.NextResponse.json({error:"Product not found"},{status:404});return i.NextResponse.json({product:r})}catch{return i.NextResponse.json({error:"Failed to fetch product"},{status:500})}}async function c(e,{params:t}){try{let{id:r}=await t,{name:s,image_url:o,net_weight:a,price:n,stock_quantity:p,category:c}=await e.json();if(!s||!a||!n||!c)return i.NextResponse.json({error:"Missing required fields"},{status:400});let{data:d,error:l}=await u.N.from("products").update({name:s,image_url:o,net_weight:a,price:parseFloat(n),stock_quantity:parseInt(p)||0,category:c}).eq("id",r).select().single();if(l)return i.NextResponse.json({error:l.message},{status:500});return i.NextResponse.json({product:d})}catch{return i.NextResponse.json({error:"Failed to update product"},{status:500})}}async function d(e,{params:t}){try{let{id:e}=await t,{error:r}=await u.N.from("products").delete().eq("id",e);if(r)return i.NextResponse.json({error:r.message},{status:500});return i.NextResponse.json({message:"Product deleted successfully"})}catch{return i.NextResponse.json({error:"Failed to delete product"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/products/[id]/route",pathname:"/api/products/[id]",filename:"route",bundlePath:"app/api/products/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\tindahan\\src\\app\\api\\products\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:_,serverHooks:N}=l;function x(){return(0,n.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:_})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},34960:(e,t,r)=>{"use strict";r.d(t,{$W:()=>i});var s=r(72289),o=r(35282);let a=s.Ik({NODE_ENV:s.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:s.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:s.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:s.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:s.Yj().optional(),CLOUDINARY_API_KEY:s.Yj().optional(),CLOUDINARY_API_SECRET:s.Yj().optional(),GEMINI_API_KEY:s.Yj().optional(),NEXTAUTH_SECRET:s.Yj().optional(),NEXTAUTH_URL:s.Yj().optional(),DEBUG:s.Yj().transform(e=>"true"===e).default("false")}),n=function(){try{return a.parse(process.env)}catch(e){if(e instanceof o.G){let t=e.errors.map(e=>`${e.path.join(".")}: ${e.message}`);throw Error(`❌ Invalid environment variables:
${t.join("\n")}

Please check your .env.local file and ensure all required variables are set.
See .env.example for reference.`)}throw e}}(),i={isDevelopment:"development"===n.NODE_ENV,isProduction:"production"===n.NODE_ENV,isTest:"test"===n.NODE_ENV,database:{url:n.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:n.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:n.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:n.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:n.CLOUDINARY_API_KEY,apiSecret:n.CLOUDINARY_API_SECRET},ai:{geminiApiKey:n.GEMINI_API_KEY},auth:{secret:n.NEXTAUTH_SECRET,url:n.NEXTAUTH_URL},debug:n.DEBUG},{NODE_ENV:u,NEXT_PUBLIC_SUPABASE_URL:p,NEXT_PUBLIC_SUPABASE_ANON_KEY:c,SUPABASE_SERVICE_ROLE_KEY:d,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:l,CLOUDINARY_API_KEY:E,CLOUDINARY_API_SECRET:_,NEXTAUTH_SECRET:N,NEXTAUTH_URL:x,DEBUG:A,GEMINI_API_KEY:U}=n},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var s=r(66437),o=r(34960);let a=(0,s.UU)(o.$W.database.url,o.$W.database.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"revantad-store@1.0.0"}}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,289,437],()=>r(16957));module.exports=s})();