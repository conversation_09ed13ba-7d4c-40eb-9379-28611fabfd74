'use client'

import { useState, useRef, useEffect } from 'react'
import { useTheme } from 'next-themes'
import {
  Send,
  Bot,
  User,
  Loader2,
  Sparkles,
  X,
  Minimize2,
  Maximize2,
  Volume2,
  VolumeX,
  RotateCcw,
  Copy,
  Check,
  Zap,
  Brain
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  isTyping?: boolean
  reactions?: string[]
}

interface AIAssistantProps {
  context?: string
}

export default function AIAssistant({ context = 'dashboard' }: AIAssistantProps) {
  const { resolvedTheme } = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Hello! I\'m your AI assistant for Revantad Store. I can help you with business analytics, inventory management, customer debt tracking, and store operations. How can I assist you today?',
      timestamp: new Date()
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSoundEnabled, setIsSoundEnabled] = useState(true)
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen, isMinimized])

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputMessage('')
    setIsLoading(true)

    // Add typing indicator
    const typingMessage: Message = {
      id: 'typing',
      type: 'ai',
      content: '',
      timestamp: new Date(),
      isTyping: true
    }
    setMessages(prev => [...prev, typingMessage])

    try {
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          context: context
        })
      })

      const data = await response.json()

      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing'))

      if (data.success) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: data.response,
          timestamp: new Date()
        }
        setMessages(prev => [...prev, aiMessage])

        // Play notification sound if enabled
        if (isSoundEnabled) {
          playNotificationSound()
        }
      } else {
        throw new Error(data.error || 'Failed to get AI response')
      }
    } catch (error) {
      // Remove typing indicator
      setMessages(prev => prev.filter(msg => msg.id !== 'typing'))

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: 'Sorry, I encountered an error. Please try again later.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const playNotificationSound = () => {
    // Create a subtle notification sound
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    const oscillator = audioContext.createOscillator()
    const gainNode = audioContext.createGain()

    oscillator.connect(gainNode)
    gainNode.connect(audioContext.destination)

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
    oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1)

    gainNode.gain.setValueAtTime(0, audioContext.currentTime)
    gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01)
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2)

    oscillator.start(audioContext.currentTime)
    oscillator.stop(audioContext.currentTime + 0.2)
  }

  const copyMessage = async (content: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopiedMessageId(messageId)
      setTimeout(() => setCopiedMessageId(null), 2000)
    } catch (error) {
      console.error('Failed to copy message:', error)
    }
  }

  const clearChat = () => {
    setMessages([
      {
        id: '1',
        type: 'ai',
        content: 'Hello! I\'m your AI assistant for Revantad Store. I can help you with business analytics, inventory management, customer debt tracking, and store operations. How can I assist you today?',
        timestamp: new Date()
      }
    ])
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    })
  }

  return (
    <>
      {/* AI Assistant Toggle Button */}
      <motion.div
        className={`fixed bottom-6 right-6 z-50 ${isOpen ? 'hidden' : 'block'}`}
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ type: 'spring', stiffness: 260, damping: 20, delay: 0.2 }}
      >
        <motion.button
          onClick={() => setIsOpen(true)}
          className="relative p-4 rounded-full shadow-2xl transition-all duration-300 focus:outline-none focus:ring-4 group overflow-hidden"
          style={{
            background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
            boxShadow: resolvedTheme === 'dark'
              ? '0 20px 40px rgba(139, 92, 246, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1)'
              : '0 20px 40px rgba(139, 92, 246, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.05)'
          }}
          whileHover={{
            scale: 1.1,
            boxShadow: resolvedTheme === 'dark'
              ? '0 25px 50px rgba(139, 92, 246, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.2)'
              : '0 25px 50px rgba(139, 92, 246, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.1)'
          }}
          whileTap={{ scale: 0.95 }}
        >
          {/* Animated background gradient */}
          <div
            className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            style={{
              background: 'linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%)'
            }}
          />

          {/* Ripple effect */}
          <div className="absolute inset-0 rounded-full">
            <div
              className="absolute inset-0 rounded-full animate-ping opacity-20"
              style={{ background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)' }}
            />
          </div>

          <div className="relative z-10 flex items-center justify-center">
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
            >
              <Brain className="w-6 h-6 text-white" />
            </motion.div>

            {/* Status indicator */}
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg">
              <div className="absolute inset-0 bg-green-400 rounded-full animate-ping opacity-75" />
            </div>

            {/* Sparkle effects */}
            <motion.div
              className="absolute -top-2 -left-2"
              animate={{
                scale: [0, 1, 0],
                rotate: [0, 180, 360]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatDelay: 1,
                delay: 0.5
              }}
            >
              <Sparkles className="w-3 h-3 text-yellow-300" />
            </motion.div>

            <motion.div
              className="absolute -bottom-2 -right-2"
              animate={{
                scale: [0, 1, 0],
                rotate: [360, 180, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatDelay: 1,
                delay: 1.5
              }}
            >
              <Zap className="w-3 h-3 text-yellow-300" />
            </motion.div>
          </div>
        </motion.button>

        {/* Tooltip */}
        <motion.div
          className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 px-3 py-2 rounded-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap"
          style={{
            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',
            border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.3)' : '1px solid rgba(229, 231, 235, 0.8)',
            boxShadow: resolvedTheme === 'dark'
              ? '0 4px 12px rgba(0, 0, 0, 0.3)'
              : '0 4px 12px rgba(0, 0, 0, 0.15)'
          }}
          initial={{ opacity: 0, x: 10 }}
          whileHover={{ opacity: 1, x: 0 }}
        >
          Ask AI Assistant
          <div
            className="absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0"
            style={{
              borderTop: '6px solid transparent',
              borderBottom: '6px solid transparent',
              borderLeft: `6px solid ${resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'}`
            }}
          />
        </motion.div>
      </motion.div>

      {/* AI Chat Interface */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className={`fixed bottom-6 right-6 z-50 rounded-2xl shadow-2xl overflow-hidden transition-all duration-300 ${
              isMinimized ? 'w-80 h-16' : 'w-96 h-[600px]'
            }`}
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid rgba(148, 163, 184, 0.3)' : '1px solid rgba(229, 231, 235, 0.8)',
              boxShadow: resolvedTheme === 'dark'
                ? '0 25px 50px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)'
                : '0 25px 50px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'
            }}
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 260, damping: 20 }}
          >
            {/* Header */}
            <div
              className="flex items-center justify-between p-4 border-b"
              style={{
                background: resolvedTheme === 'dark'
                  ? 'linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(124, 58, 237, 0.15) 100%)'
                  : 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.08) 100%)',
                borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.2)' : 'rgba(229, 231, 235, 0.8)'
              }}
            >
              <div className="flex items-center space-x-3">
                <div
                  className="p-2 rounded-lg"
                  style={{
                    background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                    boxShadow: '0 4px 8px rgba(139, 92, 246, 0.3)'
                  }}
                >
                  <Bot className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3
                    className="font-semibold text-sm"
                    style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                  >
                    AI Assistant
                  </h3>
                  <p
                    className="text-xs"
                    style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                  >
                    Revantad Store Helper
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => setIsSoundEnabled(!isSoundEnabled)}
                  className="p-1.5 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-105"
                  title={isSoundEnabled ? 'Disable sound' : 'Enable sound'}
                >
                  {isSoundEnabled ? (
                    <Volume2 className="w-4 h-4" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                  ) : (
                    <VolumeX className="w-4 h-4" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                  )}
                </button>
                <button
                  onClick={clearChat}
                  className="p-1.5 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-105"
                  title="Clear chat"
                >
                  <RotateCcw className="w-4 h-4" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                </button>
                <button
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="p-1.5 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-105"
                  title={isMinimized ? 'Maximize' : 'Minimize'}
                >
                  {isMinimized ? (
                    <Maximize2 className="w-4 h-4" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                  ) : (
                    <Minimize2 className="w-4 h-4" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                  )}
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1.5 rounded-lg transition-all duration-200 hover:bg-red-100 dark:hover:bg-red-900/20 hover:scale-105"
                  title="Close chat"
                >
                  <X className="w-4 h-4 hover:text-red-500" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                </button>
              </div>
            </div>

            {!isMinimized && (
              <>
                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4 h-[480px] chat-scroll">
                  {messages.map((message) => {
                    if (message.isTyping) {
                      return (
                        <motion.div
                          key={message.id}
                          className="flex justify-start"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                        >
                          <div className="flex items-start space-x-2">
                            <div
                              className="p-2 rounded-full"
                              style={{ backgroundColor: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }}
                            >
                              <Bot className="w-4 h-4 text-white" />
                            </div>
                            <div
                              className="p-3 rounded-2xl rounded-tl-md flex items-center space-x-2"
                              style={{
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)'
                              }}
                            >
                              <div className="flex space-x-1">
                                <motion.div
                                  className="w-2 h-2 rounded-full"
                                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }}
                                  animate={{ scale: [1, 1.2, 1] }}
                                  transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
                                />
                                <motion.div
                                  className="w-2 h-2 rounded-full"
                                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }}
                                  animate={{ scale: [1, 1.2, 1] }}
                                  transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
                                />
                                <motion.div
                                  className="w-2 h-2 rounded-full"
                                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed' }}
                                  animate={{ scale: [1, 1.2, 1] }}
                                  transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
                                />
                              </div>
                              <span className="text-sm" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>
                                AI is thinking...
                              </span>
                            </div>
                          </div>
                        </motion.div>
                      )
                    }

                    return (
                      <motion.div
                        key={message.id}
                        className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} group`}
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{ duration: 0.3, type: 'spring', stiffness: 200 }}
                      >
                        <div className={`flex items-start space-x-2 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                          <motion.div
                            className="p-2 rounded-full flex-shrink-0 relative overflow-hidden"
                            style={{
                              backgroundColor: message.type === 'user'
                                ? (resolvedTheme === 'dark' ? '#22c55e' : '#16a34a')
                                : (resolvedTheme === 'dark' ? '#8b5cf6' : '#7c3aed')
                            }}
                            whileHover={{ scale: 1.1 }}
                          >
                            {/* Shimmer effect */}
                            <div
                              className="absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-300"
                              style={{
                                background: 'linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)',
                                transform: 'translateX(-100%)',
                                animation: 'shimmer 1.5s infinite'
                              }}
                            />
                            {message.type === 'user' ? (
                              <User className="w-4 h-4 text-white relative z-10" />
                            ) : (
                              <Bot className="w-4 h-4 text-white relative z-10" />
                            )}
                          </motion.div>
                          <div className="relative">
                            <motion.div
                              className={`p-3 rounded-2xl ${message.type === 'user' ? 'rounded-tr-md' : 'rounded-tl-md'} relative overflow-hidden`}
                              style={{
                                backgroundColor: message.type === 'user'
                                  ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)')
                                  : (resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.3)' : 'rgba(243, 244, 246, 0.8)'),
                                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',
                                border: `1px solid ${message.type === 'user'
                                  ? (resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.3)' : 'rgba(34, 197, 94, 0.2)')
                                  : (resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.3)' : 'rgba(139, 92, 246, 0.2)')}`
                              }}
                              whileHover={{ scale: 1.02 }}
                            >
                              <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
                              <div className="flex items-center justify-between mt-2">
                                <p
                                  className="text-xs opacity-70"
                                  style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                                >
                                  {formatTime(message.timestamp)}
                                </p>
                                {message.type === 'ai' && (
                                  <button
                                    onClick={() => copyMessage(message.content, message.id)}
                                    className="opacity-0 group-hover:opacity-100 p-1 rounded transition-all duration-200 hover:bg-gray-200 dark:hover:bg-gray-600"
                                    title="Copy message"
                                  >
                                    {copiedMessageId === message.id ? (
                                      <Check className="w-3 h-3 text-green-500" />
                                    ) : (
                                      <Copy className="w-3 h-3" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }} />
                                    )}
                                  </button>
                                )}
                              </div>
                            </motion.div>
                          </div>
                        </div>
                      </motion.div>
                    )
                  })}

                  <div ref={messagesEndRef} />
                </div>

                {/* Input */}
                <div
                  className="p-5 border-t backdrop-blur-sm"
                  style={{
                    borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.2)' : 'rgba(229, 231, 235, 0.8)',
                    background: resolvedTheme === 'dark'
                      ? 'linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)'
                      : 'linear-gradient(135deg, rgba(249, 250, 251, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%)'
                  }}
                >
                  <div className="flex items-end space-x-3">
                    <div className="flex-1 relative">
                      <textarea
                        ref={inputRef as any}
                        value={inputMessage}
                        onChange={(e) => setInputMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="Ask me anything about your store..."
                        disabled={isLoading}
                        rows={2}
                        className="w-full p-4 pr-14 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none overflow-y-auto"
                        style={{
                          backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                          borderColor: resolvedTheme === 'dark' ? 'rgba(148, 163, 184, 0.3)' : 'rgba(229, 231, 235, 0.8)',
                          color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827',
                          minHeight: '60px',
                          maxHeight: '140px',
                          lineHeight: '1.5',
                          fontSize: '14px'
                        }}
                        onInput={(e) => {
                          const target = e.target as HTMLTextAreaElement
                          target.style.height = 'auto'
                          const newHeight = Math.min(Math.max(target.scrollHeight, 60), 140)
                          target.style.height = newHeight + 'px'
                        }}
                      />

                      {/* Character count */}
                      <div
                        className="absolute bottom-3 right-4 text-xs opacity-60 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md"
                        style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#64748b' }}
                      >
                        {inputMessage.length}/500
                      </div>
                    </div>

                    <motion.button
                      onClick={sendMessage}
                      disabled={!inputMessage.trim() || isLoading}
                      className="p-3.5 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500 relative overflow-hidden self-end"
                      style={{
                        background: !inputMessage.trim() || isLoading
                          ? (resolvedTheme === 'dark' ? 'rgba(71, 85, 105, 0.5)' : 'rgba(156, 163, 175, 0.5)')
                          : 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                        boxShadow: !inputMessage.trim() || isLoading
                          ? 'none'
                          : '0 4px 8px rgba(139, 92, 246, 0.3)',
                        minHeight: '44px',
                        minWidth: '44px'
                      }}
                      whileHover={!inputMessage.trim() || isLoading ? {} : { scale: 1.05 }}
                      whileTap={!inputMessage.trim() || isLoading ? {} : { scale: 0.95 }}
                    >
                      {/* Ripple effect */}
                      {inputMessage.trim() && !isLoading && (
                        <div className="absolute inset-0 rounded-xl">
                          <div
                            className="absolute inset-0 rounded-xl animate-pulse opacity-20"
                            style={{ background: 'linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%)' }}
                          />
                        </div>
                      )}

                      <div className="relative z-10">
                        {isLoading ? (
                          <Loader2 className="w-4 h-4 text-white animate-spin" />
                        ) : (
                          <Send className="w-4 h-4 text-white" />
                        )}
                      </div>
                    </motion.button>
                  </div>

                  {/* Quick suggestions */}
                  {!inputMessage && (
                    <motion.div
                      className="mt-2 flex flex-wrap gap-1"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      {['Sales help', 'Inventory tips', 'Customer advice'].map((suggestion, index) => (
                        <motion.button
                          key={suggestion}
                          onClick={() => setInputMessage(suggestion)}
                          className="px-2 py-1 text-xs rounded-full border transition-all duration-200 hover:scale-105"
                          style={{
                            backgroundColor: resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.1)' : 'rgba(139, 92, 246, 0.05)',
                            borderColor: resolvedTheme === 'dark' ? 'rgba(139, 92, 246, 0.3)' : 'rgba(139, 92, 246, 0.2)',
                            color: resolvedTheme === 'dark' ? '#a855f7' : '#7c3aed'
                          }}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.3 + index * 0.1 }}
                        >
                          {suggestion}
                        </motion.button>
                      ))}
                    </motion.div>
                  )}
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
