{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/APIGraphing.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState, useMemo, useCallback } from 'react'\nimport ReactECharts from 'echarts-for-react'\nimport { useTheme } from 'next-themes'\nimport {\n  TrendingUp,\n  DollarSign,\n  Package,\n  Users,\n  Activity,\n  Target,\n  Zap,\n  Filter,\n  Download,\n  RefreshCw,\n  Settings,\n  Eye,\n  TrendingDown,\n  CheckCircle,\n  Clock,\n  ArrowUp,\n  ArrowDown,\n  Minus\n} from 'lucide-react'\n\nimport type { DashboardStats } from '@/types'\n\n// Advanced data types for enhanced analytics\ninterface ChartData {\n  salesData: number[]\n  debtData: number[]\n  categoryData: { name: string; value: number; color: string }[]\n  trendData: { month: string; sales: number; debt: number; profit: number }[]\n  performanceMetrics: {\n    revenue: { current: number; previous: number; change: number }\n    customers: { current: number; previous: number; change: number }\n    products: { current: number; previous: number; change: number }\n    efficiency: { current: number; previous: number; change: number }\n  }\n}\n\ninterface FilterOptions {\n  dateRange: 'week' | 'month' | 'quarter' | 'year'\n  chartType: 'line' | 'bar' | 'area'\n  dataType: 'revenue' | 'customers' | 'products' | 'all'\n  showTrends: boolean\n  showForecasting: boolean\n}\n\ninterface APIGraphingProps {\n  stats: DashboardStats\n}\n\nexport default function APIGraphing({ stats }: APIGraphingProps) {\n  const { resolvedTheme } = useTheme()\n  const [chartData, setChartData] = useState<ChartData>({\n    salesData: [],\n    debtData: [],\n    categoryData: [],\n    trendData: [],\n    performanceMetrics: {\n      revenue: { current: 0, previous: 0, change: 0 },\n      customers: { current: 0, previous: 0, change: 0 },\n      products: { current: 0, previous: 0, change: 0 },\n      efficiency: { current: 0, previous: 0, change: 0 }\n    }\n  })\n\n  const [filters, setFilters] = useState<FilterOptions>({\n    dateRange: 'month',\n    chartType: 'line',\n    dataType: 'all',\n    showTrends: true,\n    showForecasting: false\n  })\n\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  const [isLoading, setIsLoading] = useState(false)\n  const [lastUpdated, setLastUpdated] = useState(new Date())\n  const [isMobile, setIsMobile] = useState(false)\n\n  // Responsive design hook\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768)\n    }\n\n    checkMobile()\n    window.addEventListener('resize', checkMobile)\n    return () => window.removeEventListener('resize', checkMobile)\n  }, [])\n\n  // Advanced data generation with realistic patterns and trends\n  const generateAdvancedData = useCallback(() => {\n    setIsLoading(true)\n\n    // Generate realistic sales data with seasonal patterns\n    const generateSalesData = () => {\n      const baseValue = 25000\n      const seasonalMultipliers = [0.8, 0.85, 0.9, 1.0, 1.1, 1.2, 1.3, 1.25, 1.15, 1.05, 0.95, 0.9]\n      return seasonalMultipliers.map((multiplier, index) => {\n        const randomVariation = (Math.random() - 0.5) * 0.2\n        return Math.floor(baseValue * multiplier * (1 + randomVariation))\n      })\n    }\n\n    // Generate debt data with weekly patterns\n    const generateDebtData = () => {\n      const baseDebt = 8000\n      const weeklyPattern = [0.9, 1.0, 1.1, 1.2, 1.3, 1.1, 0.8] // Mon-Sun pattern\n      return weeklyPattern.map(multiplier => {\n        const randomVariation = (Math.random() - 0.5) * 0.15\n        return Math.floor(baseDebt * multiplier * (1 + randomVariation))\n      })\n    }\n\n    // Generate category distribution data\n    const generateCategoryData = () => {\n      const categories = [\n        { name: 'Beverages', value: 35, color: '#22c55e' },\n        { name: 'Snacks', value: 28, color: '#3b82f6' },\n        { name: 'Household', value: 20, color: '#f59e0b' },\n        { name: 'Personal Care', value: 12, color: '#ef4444' },\n        { name: 'Others', value: 5, color: '#8b5cf6' }\n      ]\n      return categories.map(cat => ({\n        ...cat,\n        value: cat.value + Math.floor((Math.random() - 0.5) * 10)\n      }))\n    }\n\n    // Generate trend data for advanced analytics\n    const generateTrendData = () => {\n      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']\n      return months.map((month, index) => {\n        const sales = 20000 + (index * 2000) + Math.floor(Math.random() * 5000)\n        const debt = 5000 + Math.floor(Math.random() * 3000)\n        const profit = sales * 0.3 - debt * 0.1\n        return { month, sales, debt, profit }\n      })\n    }\n\n    // Calculate performance metrics with comparisons\n    const calculatePerformanceMetrics = (salesData: number[], currentStats: DashboardStats) => {\n      const currentRevenue = salesData.reduce((a, b) => a + b, 0)\n      const previousRevenue = currentRevenue * (0.85 + Math.random() * 0.3)\n\n      return {\n        revenue: {\n          current: currentRevenue,\n          previous: previousRevenue,\n          change: ((currentRevenue - previousRevenue) / previousRevenue) * 100\n        },\n        customers: {\n          current: currentStats.totalDebts,\n          previous: Math.floor(currentStats.totalDebts * (0.9 + Math.random() * 0.2)),\n          change: Math.floor((Math.random() - 0.5) * 20)\n        },\n        products: {\n          current: currentStats.totalProducts,\n          previous: Math.floor(currentStats.totalProducts * (0.95 + Math.random() * 0.1)),\n          change: Math.floor((Math.random() - 0.3) * 15)\n        },\n        efficiency: {\n          current: 85 + Math.floor(Math.random() * 10),\n          previous: 80 + Math.floor(Math.random() * 10),\n          change: Math.floor((Math.random() - 0.4) * 10)\n        }\n      }\n    }\n\n    // Simulate API delay for realistic loading experience\n    setTimeout(() => {\n      const salesData = generateSalesData()\n      const debtData = generateDebtData()\n      const categoryData = generateCategoryData()\n      const trendData = generateTrendData()\n      const performanceMetrics = calculatePerformanceMetrics(salesData, stats)\n\n      setChartData({\n        salesData,\n        debtData,\n        categoryData,\n        trendData,\n        performanceMetrics\n      })\n\n      setLastUpdated(new Date())\n      setIsLoading(false)\n    }, 800)\n  }, [stats])\n\n  useEffect(() => {\n    generateAdvancedData()\n  }, [generateAdvancedData, filters.dateRange])\n\n  // Advanced chart configurations with theme support\n  const getChartTheme = () => ({\n    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n    textStyle: {\n      color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937',\n      fontFamily: 'Inter, system-ui, sans-serif'\n    },\n    grid: {\n      borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'\n    }\n  })\n\n  // Enhanced Sales Chart with advanced features\n  const salesChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Monthly Sales Revenue',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'axis',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const data = params[0]\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${data.name}</div>\n            <div style=\"display: flex; align-items: center;\">\n              <div style=\"width: 10px; height: 10px; background: ${data.color}; border-radius: 50%; margin-right: 8px;\"></div>\n              Revenue: ₱${data.value.toLocaleString()}\n            </div>\n            <div style=\"font-size: 12px; color: #6b7280; margin-top: 4px;\">\n              ${data.value > (chartData.salesData[data.dataIndex - 1] || 0) ? '↗️ Increased' : '↘️ Decreased'} from previous month\n            </div>\n          </div>\n        `\n      }\n    },\n    legend: {\n      show: true,\n      top: 40,\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#cbd5e1' : '#4b5563'\n      }\n    },\n    xAxis: {\n      type: 'category',\n      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n        }\n      },\n      axisLabel: {\n        color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n        fontSize: 12\n      }\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n        color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n        fontSize: 12\n      },\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n        }\n      },\n      splitLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#334155' : '#f3f4f6',\n          type: 'dashed'\n        }\n      }\n    },\n    series: [\n      {\n        name: 'Revenue',\n        data: chartData.salesData,\n        type: filters.chartType,\n        smooth: true,\n        lineStyle: {\n          color: '#22c55e',\n          width: 3\n        },\n        itemStyle: {\n          color: '#22c55e',\n          borderRadius: filters.chartType === 'bar' ? [4, 4, 0, 0] : 0\n        },\n        areaStyle: filters.chartType === 'area' ? {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: 'rgba(34, 197, 94, 0.4)' },\n              { offset: 1, color: 'rgba(34, 197, 94, 0.05)' }\n            ]\n          }\n        } : undefined,\n        emphasis: {\n          focus: 'series',\n          itemStyle: {\n            shadowBlur: 10,\n            shadowColor: 'rgba(34, 197, 94, 0.5)'\n          }\n        },\n        markPoint: {\n          data: [\n            { type: 'max', name: 'Max' },\n            { type: 'min', name: 'Min' }\n          ],\n          itemStyle: {\n            color: '#facc15'\n          }\n        },\n        markLine: filters.showTrends ? {\n          data: [\n            { type: 'average', name: 'Average' }\n          ],\n          lineStyle: {\n            color: '#f59e0b',\n            type: 'dashed'\n          }\n        } : undefined\n      }\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '10%',\n      top: '15%',\n      containLabel: true\n    },\n    dataZoom: [\n      {\n        type: 'inside',\n        start: 0,\n        end: 100\n      },\n      {\n        start: 0,\n        end: 100,\n        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',\n        handleSize: '80%',\n        handleStyle: {\n          color: '#22c55e',\n          shadowBlur: 3,\n          shadowColor: 'rgba(0, 0, 0, 0.6)',\n          shadowOffsetX: 2,\n          shadowOffsetY: 2\n        }\n      }\n    ],\n    toolbox: {\n      feature: {\n        dataZoom: {\n          yAxisIndex: 'none'\n        },\n        restore: {},\n        saveAsImage: {\n          pixelRatio: 2\n        }\n      },\n      iconStyle: {\n        borderColor: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      }\n    }\n  }), [chartData.salesData, filters.chartType, resolvedTheme])\n\n  // Enhanced Debt Chart with advanced features\n  const debtChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Weekly Customer Debt Trends',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    tooltip: {\n      trigger: 'axis',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        const data = params[0]\n        const percentage = ((data.value / chartData.debtData.reduce((a: number, b: number) => a + b, 0)) * 100).toFixed(1)\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${data.name}</div>\n            <div style=\"display: flex; align-items: center;\">\n              <div style=\"width: 10px; height: 10px; background: ${data.color}; border-radius: 2px; margin-right: 8px;\"></div>\n              Total Debt: ₱${data.value.toLocaleString()}\n            </div>\n            <div style=\"font-size: 12px; color: #6b7280; margin-top: 4px;\">\n              ${percentage}% of weekly total\n            </div>\n          </div>\n        `\n      }\n    },\n    legend: {\n      show: true,\n      top: 40,\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#cbd5e1' : '#4b5563'\n      }\n    },\n    xAxis: {\n      type: 'category',\n      data: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n        }\n      },\n      axisLabel: {\n        color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n        fontSize: 12,\n        rotate: 45\n      }\n    },\n    yAxis: {\n      type: 'value',\n      axisLabel: {\n        formatter: '₱{value}',\n        color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n        fontSize: 12\n      },\n      axisLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n        }\n      },\n      splitLine: {\n        lineStyle: {\n          color: resolvedTheme === 'dark' ? '#334155' : '#f3f4f6',\n          type: 'dashed'\n        }\n      }\n    },\n    series: [\n      {\n        name: 'Customer Debt',\n        data: chartData.debtData,\n        type: 'bar',\n        itemStyle: {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [\n              { offset: 0, color: '#facc15' },\n              { offset: 1, color: '#eab308' }\n            ]\n          },\n          borderRadius: [4, 4, 0, 0]\n        },\n        emphasis: {\n          focus: 'series',\n          itemStyle: {\n            color: '#f59e0b',\n            shadowBlur: 10,\n            shadowColor: 'rgba(245, 158, 11, 0.5)'\n          }\n        },\n        markPoint: {\n          data: [\n            { type: 'max', name: 'Peak Day' },\n            { type: 'min', name: 'Low Day' }\n          ],\n          itemStyle: {\n            color: '#ef4444'\n          }\n        }\n      }\n    ],\n    grid: {\n      left: '3%',\n      right: '4%',\n      bottom: '15%',\n      top: '15%',\n      containLabel: true\n    },\n    toolbox: {\n      feature: {\n        dataZoom: {\n          yAxisIndex: 'none'\n        },\n        restore: {},\n        saveAsImage: {\n          pixelRatio: 2\n        }\n      },\n      iconStyle: {\n        borderColor: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n      }\n    }\n  }), [chartData.debtData, resolvedTheme])\n\n  // Enhanced Product Categories Pie Chart\n  const categoryChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Product Categories Distribution',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 15\n    },\n    tooltip: {\n      trigger: 'item',\n      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n      },\n      formatter: (params: any) => {\n        return `\n          <div style=\"padding: 8px;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${params.name}</div>\n            <div style=\"display: flex; align-items: center;\">\n              <div style=\"width: 10px; height: 10px; background: ${params.color}; border-radius: 50%; margin-right: 8px;\"></div>\n              Value: ${params.value}%\n            </div>\n            <div style=\"font-size: 12px; color: #6b7280; margin-top: 4px;\">\n              ${params.percent}% of total sales\n            </div>\n          </div>\n        `\n      }\n    },\n    legend: {\n      orient: 'horizontal',\n      bottom: 15,\n      textStyle: {\n        color: resolvedTheme === 'dark' ? '#cbd5e1' : '#4b5563',\n        fontSize: 11\n      }\n    },\n    grid: {\n      top: 60,\n      bottom: 60,\n      left: 40,\n      right: 40,\n      containLabel: true\n    },\n    series: [\n      {\n        name: 'Categories',\n        type: 'pie',\n        radius: ['35%', '60%'], // Reduced radius to provide more space for labels\n        center: ['50%', '55%'], // Moved center slightly down to accommodate title\n        avoidLabelOverlap: true, // Enable label overlap avoidance\n        itemStyle: {\n          borderRadius: 8,\n          borderColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n          borderWidth: 2\n        },\n        label: {\n          show: true,\n          position: 'outside',\n          formatter: '{b}: {c}%',\n          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#4b5563',\n          fontSize: 11,\n          fontWeight: '500',\n          distanceToLabelLine: 5 // Reduce distance to label line\n        },\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowOffsetX: 0,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          },\n          label: {\n            show: true,\n            fontSize: 12,\n            fontWeight: 'bold'\n          }\n        },\n        labelLine: {\n          show: true,\n          length: 15, // Shorter label lines\n          length2: 8,\n          lineStyle: {\n            color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',\n            width: 1\n          }\n        },\n        data: chartData.categoryData\n      }\n    ]\n  }), [chartData.categoryData, resolvedTheme])\n\n  // Advanced Heatmap Chart for hourly sales patterns\n  const heatmapChartOption = useMemo(() => {\n    const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)\n    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']\n    const heatmapData = []\n\n    for (let day = 0; day < 7; day++) {\n      for (let hour = 0; hour < 24; hour++) {\n        const value = Math.floor(Math.random() * 100) + 10\n        heatmapData.push([hour, day, value])\n      }\n    }\n\n    return {\n      ...getChartTheme(),\n      title: {\n        text: 'Sales Activity Heatmap',\n        textStyle: {\n          fontSize: 18,\n          fontWeight: 'bold',\n          color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n        },\n        left: 'center',\n        top: 10\n      },\n      tooltip: {\n        position: 'top',\n        backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n        borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb',\n        textStyle: {\n          color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n        },\n        formatter: (params: any) => {\n          return `\n            <div style=\"padding: 8px;\">\n              <div style=\"font-weight: bold; margin-bottom: 4px;\">${days[params.data[1]]} ${hours[params.data[0]]}</div>\n              <div style=\"display: flex; align-items: center;\">\n                <div style=\"width: 10px; height: 10px; background: ${params.color}; border-radius: 2px; margin-right: 8px;\"></div>\n                Sales Activity: ${params.data[2]}%\n              </div>\n            </div>\n          `\n        }\n      },\n      grid: {\n        height: '60%',\n        top: '15%'\n      },\n      xAxis: {\n        type: 'category',\n        data: hours,\n        splitArea: {\n          show: true\n        },\n        axisLabel: {\n          color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n          fontSize: 10\n        }\n      },\n      yAxis: {\n        type: 'category',\n        data: days,\n        splitArea: {\n          show: true\n        },\n        axisLabel: {\n          color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n          fontSize: 12\n        }\n      },\n      visualMap: {\n        min: 0,\n        max: 100,\n        calculable: true,\n        orient: 'horizontal',\n        left: 'center',\n        bottom: '5%',\n        inRange: {\n          color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']\n        },\n        textStyle: {\n          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#4b5563'\n        }\n      },\n      series: [{\n        name: 'Sales Activity',\n        type: 'heatmap',\n        data: heatmapData,\n        label: {\n          show: false\n        },\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          }\n        }\n      }]\n    }\n  }, [resolvedTheme])\n\n  // Gauge Chart for performance metrics\n  const gaugeChartOption = useMemo(() => ({\n    ...getChartTheme(),\n    title: {\n      text: 'Business Performance',\n      textStyle: {\n        fontSize: 18,\n        fontWeight: 'bold',\n        color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n      },\n      left: 'center',\n      top: 10\n    },\n    series: [\n      {\n        name: 'Performance',\n        type: 'gauge',\n        center: ['50%', '60%'],\n        startAngle: 200,\n        endAngle: -40,\n        min: 0,\n        max: 100,\n        splitNumber: 10,\n        itemStyle: {\n          color: '#22c55e'\n        },\n        progress: {\n          show: true,\n          width: 30\n        },\n        pointer: {\n          show: false\n        },\n        axisLine: {\n          lineStyle: {\n            width: 30,\n            color: [\n              [0.3, '#ef4444'],\n              [0.7, '#f59e0b'],\n              [1, '#22c55e']\n            ]\n          }\n        },\n        axisTick: {\n          distance: -45,\n          splitNumber: 5,\n          lineStyle: {\n            width: 2,\n            color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n          }\n        },\n        splitLine: {\n          distance: -52,\n          length: 14,\n          lineStyle: {\n            width: 3,\n            color: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n          }\n        },\n        axisLabel: {\n          distance: -20,\n          color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280',\n          fontSize: 12\n        },\n        anchor: {\n          show: false\n        },\n        title: {\n          show: false\n        },\n        detail: {\n          valueAnimation: true,\n          width: '60%',\n          lineHeight: 40,\n          borderRadius: 8,\n          offsetCenter: [0, '-15%'],\n          fontSize: 24,\n          fontWeight: 'bold',\n          formatter: '{value}%',\n          color: resolvedTheme === 'dark' ? '#f1f5f9' : '#1f2937'\n        },\n        data: [\n          {\n            value: chartData.performanceMetrics.efficiency.current,\n            name: 'Efficiency'\n          }\n        ]\n      }\n    ]\n  }), [chartData.performanceMetrics.efficiency.current, resolvedTheme])\n\n  // Enhanced KPI calculations with advanced metrics\n  const kpiCards = useMemo(() => {\n    const totalRevenue = chartData.salesData.reduce((a: number, b: number) => a + b, 0)\n    const avgMonthlyRevenue = totalRevenue / chartData.salesData.length\n    const totalDebt = chartData.debtData.reduce((a: number, b: number) => a + b, 0)\n    const avgDailyDebt = totalDebt / chartData.debtData.length\n\n    return [\n      {\n        title: 'Total Revenue',\n        value: '₱' + totalRevenue.toLocaleString(),\n        icon: DollarSign,\n        color: 'text-green-600 dark:text-green-400',\n        bgColor: 'bg-green-50 dark:bg-green-900/20',\n        change: '+12.5%',\n        changeColor: 'text-green-600 dark:text-green-400',\n        trend: 'up',\n        subtitle: `Avg: ₱${avgMonthlyRevenue.toLocaleString()}/month`\n      },\n      {\n        title: 'Active Customers',\n        value: chartData.performanceMetrics.customers.current.toString(),\n        icon: Users,\n        color: 'text-blue-600 dark:text-blue-400',\n        bgColor: 'bg-blue-50 dark:bg-blue-900/20',\n        change: `${chartData.performanceMetrics.customers.change > 0 ? '+' : ''}${chartData.performanceMetrics.customers.change.toFixed(1)}%`,\n        changeColor: chartData.performanceMetrics.customers.change > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400',\n        trend: chartData.performanceMetrics.customers.change > 0 ? 'up' : 'down',\n        subtitle: 'Customer base growth'\n      },\n      {\n        title: 'Products Listed',\n        value: chartData.performanceMetrics.products.current.toString(),\n        icon: Package,\n        color: 'text-purple-600 dark:text-purple-400',\n        bgColor: 'bg-purple-50 dark:bg-purple-900/20',\n        change: `${chartData.performanceMetrics.products.change > 0 ? '+' : ''}${chartData.performanceMetrics.products.change.toFixed(1)}%`,\n        changeColor: chartData.performanceMetrics.products.change > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400',\n        trend: chartData.performanceMetrics.products.change > 0 ? 'up' : 'down',\n        subtitle: 'Inventory expansion'\n      },\n      {\n        title: 'Business Efficiency',\n        value: `${chartData.performanceMetrics.efficiency.current}%`,\n        icon: Target,\n        color: 'text-orange-600 dark:text-orange-400',\n        bgColor: 'bg-orange-50 dark:bg-orange-900/20',\n        change: `${chartData.performanceMetrics.efficiency.change > 0 ? '+' : ''}${chartData.performanceMetrics.efficiency.change.toFixed(1)}%`,\n        changeColor: chartData.performanceMetrics.efficiency.change > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400',\n        trend: chartData.performanceMetrics.efficiency.change > 0 ? 'up' : 'down',\n        subtitle: 'Operational performance'\n      },\n      {\n        title: 'Weekly Debt Avg',\n        value: '₱' + avgDailyDebt.toLocaleString(),\n        icon: TrendingDown,\n        color: 'text-yellow-600 dark:text-yellow-400',\n        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',\n        change: '-3.2%',\n        changeColor: 'text-green-600 dark:text-green-400',\n        trend: 'down',\n        subtitle: 'Daily average debt'\n      },\n      {\n        title: 'Growth Rate',\n        value: `${((totalRevenue / (totalRevenue * 0.85)) * 100 - 100).toFixed(1)}%`,\n        icon: TrendingUp,\n        color: 'text-emerald-600 dark:text-emerald-400',\n        bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',\n        change: '+15.8%',\n        changeColor: 'text-green-600 dark:text-green-400',\n        trend: 'up',\n        subtitle: 'Monthly growth rate'\n      }\n    ]\n  }, [chartData, stats.totalDebtAmount])\n\n  // Filter controls component\n  const FilterControls = () => (\n    <div className=\"card p-4 mb-6\">\n      <div className=\"flex flex-wrap items-center justify-between gap-4\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-2\">\n            <Filter className=\"h-4 w-4 text-gray-500 dark:text-gray-400\" />\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">Filters:</span>\n          </div>\n\n          <select\n            value={filters.dateRange}\n            onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value as FilterOptions['dateRange'] }))}\n            className=\"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n          >\n            <option value=\"week\">This Week</option>\n            <option value=\"month\">This Month</option>\n            <option value=\"quarter\">This Quarter</option>\n            <option value=\"year\">This Year</option>\n          </select>\n\n          <select\n            value={filters.chartType}\n            onChange={(e) => setFilters(prev => ({ ...prev, chartType: e.target.value as FilterOptions['chartType'] }))}\n            className=\"px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n          >\n            <option value=\"line\">Line Chart</option>\n            <option value=\"bar\">Bar Chart</option>\n            <option value=\"area\">Area Chart</option>\n          </select>\n        </div>\n\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={generateAdvancedData}\n            disabled={isLoading}\n            className=\"flex items-center space-x-2 px-3 py-1 text-sm bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white rounded-md transition-colors duration-200\"\n          >\n            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />\n            <span>{isLoading ? 'Updating...' : 'Refresh'}</span>\n          </button>\n\n          <button className=\"flex items-center space-x-2 px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors duration-200\">\n            <Download className=\"h-4 w-4\" />\n            <span>Export</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Filter Controls */}\n      <FilterControls />\n\n      {/* Enhanced KPI Cards - Responsive Grid */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3 sm:gap-4\">\n        {kpiCards.map((kpi, index) => (\n          <div key={index} className=\"card p-4 hover:shadow-lg transition-all duration-300 group\">\n            <div className=\"flex flex-col space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <div className={`p-2 rounded-lg ${kpi.bgColor} group-hover:scale-110 transition-transform duration-200`}>\n                  <kpi.icon className={`h-5 w-5 ${kpi.color}`} />\n                </div>\n                <div className=\"flex items-center space-x-1\">\n                  {kpi.trend === 'up' && <ArrowUp className=\"h-3 w-3 text-green-500\" />}\n                  {kpi.trend === 'down' && <ArrowDown className=\"h-3 w-3 text-red-500\" />}\n                  {kpi.trend === 'neutral' && <Minus className=\"h-3 w-3 text-gray-500\" />}\n                </div>\n              </div>\n\n              <div>\n                <p className=\"text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide\">\n                  {kpi.title}\n                </p>\n                <p className=\"text-lg font-bold text-gray-900 dark:text-white mt-1\">\n                  {kpi.value}\n                </p>\n                {kpi.subtitle && (\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                    {kpi.subtitle}\n                  </p>\n                )}\n                <div className=\"flex items-center justify-between mt-2\">\n                  <span className={`text-xs font-medium ${kpi.changeColor}`}>\n                    {kpi.change}\n                  </span>\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    vs last period\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Enhanced Charts Grid - Responsive Layout */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6\">\n        {/* Sales Revenue Chart */}\n        <div className=\"card p-6 hover:shadow-lg transition-shadow duration-300\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"p-2 bg-green-100 dark:bg-green-900/20 rounded-lg\">\n                <TrendingUp className=\"h-5 w-5 text-green-600 dark:text-green-400\" />\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Sales Revenue</h3>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Monthly performance trends</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center space-x-1\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">Live</span>\n              </div>\n            </div>\n          </div>\n          {isLoading ? (\n            <div className=\"flex items-center justify-center h-96\">\n              <div className=\"flex flex-col items-center space-y-4\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500\"></div>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Loading chart data...</p>\n              </div>\n            </div>\n          ) : (\n            <ReactECharts\n              option={salesChartOption}\n              style={{ height: isMobile ? '300px' : '400px' }}\n            />\n          )}\n        </div>\n\n        {/* Customer Debt Chart */}\n        <div className=\"card p-6 hover:shadow-lg transition-shadow duration-300\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg\">\n                <Users className=\"h-5 w-5 text-yellow-600 dark:text-yellow-400\" />\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Customer Debt</h3>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Weekly debt patterns</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center space-x-1\">\n                <div className=\"w-2 h-2 bg-yellow-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">Active</span>\n              </div>\n            </div>\n          </div>\n          {isLoading ? (\n            <div className=\"flex items-center justify-center h-96\">\n              <div className=\"flex flex-col items-center space-y-4\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500\"></div>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Loading chart data...</p>\n              </div>\n            </div>\n          ) : (\n            <ReactECharts\n              option={debtChartOption}\n              style={{ height: isMobile ? '300px' : '400px' }}\n            />\n          )}\n        </div>\n      </div>\n\n      {/* Advanced Visualization Grid - Mobile Optimized */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6\">\n        {/* Category Distribution Chart */}\n        <div className=\"card p-6 hover:shadow-lg transition-shadow duration-300\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg\">\n                <Package className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Product Categories</h3>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Sales distribution by category</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <button className=\"p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200\">\n                <Eye className=\"h-4 w-4 text-gray-500 dark:text-gray-400\" />\n              </button>\n              <button className=\"p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200\">\n                <Settings className=\"h-4 w-4 text-gray-500 dark:text-gray-400\" />\n              </button>\n            </div>\n          </div>\n\n          {/* Category Filter Buttons */}\n          <div className=\"mb-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-200 dark:border-gray-700\">\n            <div className=\"flex flex-col sm:flex-row sm:items-center gap-4\">\n              <span className=\"text-sm font-semibold text-gray-700 dark:text-gray-300 whitespace-nowrap\">\n                Filter by Category:\n              </span>\n              <div className=\"flex flex-wrap items-center gap-2\">\n                {[\n                  { id: 'all', label: 'All Categories', color: 'bg-gray-500', hoverColor: 'hover:bg-gray-600' },\n                  { id: 'beverages', label: 'Beverages', color: 'bg-green-500', hoverColor: 'hover:bg-green-600' },\n                  { id: 'snacks', label: 'Snacks', color: 'bg-blue-500', hoverColor: 'hover:bg-blue-600' },\n                  { id: 'household', label: 'Household', color: 'bg-amber-500', hoverColor: 'hover:bg-amber-600' },\n                  { id: 'personal-care', label: 'Personal Care', color: 'bg-red-500', hoverColor: 'hover:bg-red-600' },\n                  { id: 'others', label: 'Others', color: 'bg-purple-500', hoverColor: 'hover:bg-purple-600' }\n                ].map((category) => (\n                  <button\n                    key={category.id}\n                    onClick={() => setSelectedCategory(category.id)}\n                    className={`\n                      px-3 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200\n                      flex items-center space-x-2 hover:scale-105 active:scale-95 focus:outline-none focus:ring-2 focus:ring-offset-2\n                      ${selectedCategory === category.id\n                        ? `${category.color} text-white shadow-lg ring-2 ring-offset-2 ring-gray-300 dark:ring-gray-600 ${category.hoverColor}`\n                        : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 shadow-sm'\n                      }\n                    `}\n                  >\n                    <div\n                      className={`w-2.5 h-2.5 rounded-full transition-colors duration-200 ${\n                        selectedCategory === category.id ? 'bg-white/40' : category.color\n                      }`}\n                    />\n                    <span className=\"whitespace-nowrap\">{category.label}</span>\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n          {isLoading ? (\n            <div className=\"flex items-center justify-center h-96\">\n              <div className=\"flex flex-col items-center space-y-4\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Loading chart data...</p>\n              </div>\n            </div>\n          ) : (\n            <ReactECharts\n              option={categoryChartOption}\n              style={{ height: isMobile ? '350px' : '450px' }}\n            />\n          )}\n        </div>\n\n        {/* Performance Gauge Chart */}\n        <div className=\"card p-6 hover:shadow-lg transition-shadow duration-300\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"p-2 bg-emerald-100 dark:bg-emerald-900/20 rounded-lg\">\n                <Target className=\"h-5 w-5 text-emerald-600 dark:text-emerald-400\" />\n              </div>\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Performance Gauge</h3>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Overall business efficiency</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center space-x-1\">\n                <div className=\"w-2 h-2 bg-emerald-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">Real-time</span>\n              </div>\n            </div>\n          </div>\n          {isLoading ? (\n            <div className=\"flex items-center justify-center h-96\">\n              <div className=\"flex flex-col items-center space-y-4\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500\"></div>\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">Loading gauge data...</p>\n              </div>\n            </div>\n          ) : (\n            <ReactECharts\n              option={gaugeChartOption}\n              style={{ height: isMobile ? '300px' : '400px' }}\n            />\n          )}\n        </div>\n      </div>\n\n      {/* Sales Activity Heatmap */}\n      <div className=\"card p-6 hover:shadow-lg transition-shadow duration-300\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg\">\n              <Activity className=\"h-5 w-5 text-purple-600 dark:text-purple-400\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Sales Activity Heatmap</h3>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">Hourly sales patterns throughout the week</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n              <span className=\"text-xs text-gray-500 dark:text-gray-400\">Pattern Analysis</span>\n            </div>\n          </div>\n        </div>\n        {isLoading ? (\n          <div className=\"flex items-center justify-center h-96\">\n            <div className=\"flex flex-col items-center space-y-4\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500\"></div>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">Loading heatmap data...</p>\n            </div>\n          </div>\n        ) : (\n          <ReactECharts\n            option={heatmapChartOption}\n            style={{ height: isMobile ? '400px' : '500px' }}\n          />\n        )}\n      </div>\n\n      {/* Advanced Analytics Dashboard */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Predictive Insights */}\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Predictive Insights</h3>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-sm text-blue-600 dark:text-blue-400 font-medium\">AI Powered</span>\n            </div>\n          </div>\n          <div className=\"space-y-4\">\n            <div className=\"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <TrendingUp className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n                <span className=\"text-sm font-medium text-blue-800 dark:text-blue-300\">Revenue Forecast</span>\n              </div>\n              <p className=\"text-xs text-blue-700 dark:text-blue-400\">\n                Expected 15% growth next month based on current trends\n              </p>\n              <div className=\"mt-2 w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2\">\n                <div className=\"bg-blue-600 h-2 rounded-full\" style={{ width: '75%' }}></div>\n              </div>\n            </div>\n\n            <div className=\"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <Users className=\"h-4 w-4 text-green-600 dark:text-green-400\" />\n                <span className=\"text-sm font-medium text-green-800 dark:text-green-300\">Customer Growth</span>\n              </div>\n              <p className=\"text-xs text-green-700 dark:text-green-400\">\n                New customer acquisition rate increasing by 8%\n              </p>\n              <div className=\"mt-2 w-full bg-green-200 dark:bg-green-800 rounded-full h-2\">\n                <div className=\"bg-green-600 h-2 rounded-full\" style={{ width: '68%' }}></div>\n              </div>\n            </div>\n\n            <div className=\"p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <Package className=\"h-4 w-4 text-yellow-600 dark:text-yellow-400\" />\n                <span className=\"text-sm font-medium text-yellow-800 dark:text-yellow-300\">Inventory Alert</span>\n              </div>\n              <p className=\"text-xs text-yellow-700 dark:text-yellow-400\">\n                3 products predicted to run low stock within 5 days\n              </p>\n              <div className=\"mt-2 w-full bg-yellow-200 dark:bg-yellow-800 rounded-full h-2\">\n                <div className=\"bg-yellow-600 h-2 rounded-full\" style={{ width: '30%' }}></div>\n              </div>\n            </div>\n          </div>\n        </div>\n        {/* Real-time Status */}\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">System Status</h3>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-sm text-green-600 dark:text-green-400 font-medium\">Online</span>\n            </div>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Activity className=\"h-4 w-4 text-green-500\" />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Data Streaming</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                <span className=\"text-sm text-green-600 dark:text-green-400\">Active</span>\n              </div>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Zap className=\"h-4 w-4 text-blue-500\" />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">API Response</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-blue-600 dark:text-blue-400\">~250ms</span>\n              </div>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Clock className=\"h-4 w-4 text-yellow-500\" />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Last Update</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {lastUpdated.toLocaleTimeString()}\n                </span>\n              </div>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <Target className=\"h-4 w-4 text-purple-500\" />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">Data Quality</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-purple-600 dark:text-purple-400\">98.5%</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Performance Metrics */}\n        <div className=\"card p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Performance</h3>\n            <div className=\"flex items-center space-x-2\">\n              <Target className=\"h-4 w-4 text-blue-500\" />\n              <span className=\"text-sm text-blue-600 dark:text-blue-400 font-medium\">Optimized</span>\n            </div>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">Chart Rendering</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div className=\"bg-green-500 h-2 rounded-full\" style={{ width: '95%' }}></div>\n                </div>\n                <span className=\"text-sm text-green-600 dark:text-green-400\">95%</span>\n              </div>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">Data Processing</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div className=\"bg-blue-500 h-2 rounded-full\" style={{ width: '88%' }}></div>\n                </div>\n                <span className=\"text-sm text-blue-600 dark:text-blue-400\">88%</span>\n              </div>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">Memory Usage</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div className=\"bg-yellow-500 h-2 rounded-full\" style={{ width: '72%' }}></div>\n                </div>\n                <span className=\"text-sm text-yellow-600 dark:text-yellow-400\">72%</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Business Intelligence Summary */}\n      <div className=\"card p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"p-2 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg\">\n              <TrendingUp className=\"h-5 w-5 text-indigo-600 dark:text-indigo-400\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Business Intelligence Summary</h3>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">Key insights and recommendations</p>\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-2 h-2 bg-indigo-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-xs text-gray-500 dark:text-gray-400\">Auto-generated</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-600 dark:text-green-400\" />\n              <span className=\"text-sm font-medium text-green-800 dark:text-green-300\">Strong Performance</span>\n            </div>\n            <p className=\"text-xs text-green-700 dark:text-green-400\">\n              Revenue growth is exceeding targets by 12%. Continue current marketing strategies.\n            </p>\n          </div>\n\n          <div className=\"p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Users className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n              <span className=\"text-sm font-medium text-blue-800 dark:text-blue-300\">Customer Retention</span>\n            </div>\n            <p className=\"text-xs text-blue-700 dark:text-blue-400\">\n              Customer loyalty programs showing positive impact. 85% retention rate achieved.\n            </p>\n          </div>\n\n          <div className=\"p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-lg\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Package className=\"h-4 w-4 text-yellow-600 dark:text-yellow-400\" />\n              <span className=\"text-sm font-medium text-yellow-800 dark:text-yellow-300\">Inventory Optimization</span>\n            </div>\n            <p className=\"text-xs text-yellow-700 dark:text-yellow-400\">\n              Consider increasing stock for high-demand items. Seasonal patterns identified.\n            </p>\n          </div>\n\n          <div className=\"p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Target className=\"h-4 w-4 text-purple-600 dark:text-purple-400\" />\n              <span className=\"text-sm font-medium text-purple-800 dark:text-purple-300\">Efficiency Gains</span>\n            </div>\n            <p className=\"text-xs text-purple-700 dark:text-purple-400\">\n              Operational efficiency improved by 15%. Focus on peak hour optimization.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg\">\n          <div className=\"flex items-center space-x-2 mb-3\">\n            <Activity className=\"h-4 w-4 text-gray-600 dark:text-gray-400\" />\n            <span className=\"text-sm font-medium text-gray-800 dark:text-gray-300\">Next Actions</span>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n              <span className=\"text-xs text-gray-700 dark:text-gray-400\">Expand successful product lines</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n              <span className=\"text-xs text-gray-700 dark:text-gray-400\">Implement customer feedback system</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-yellow-500 rounded-full\"></div>\n              <span className=\"text-xs text-gray-700 dark:text-gray-400\">Optimize inventory turnover</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAsDe,SAAS,YAAY,EAAE,KAAK,EAAoB;;IAC7D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,WAAW,EAAE;QACb,UAAU,EAAE;QACZ,cAAc,EAAE;QAChB,WAAW,EAAE;QACb,oBAAoB;YAClB,SAAS;gBAAE,SAAS;gBAAG,UAAU;gBAAG,QAAQ;YAAE;YAC9C,WAAW;gBAAE,SAAS;gBAAG,UAAU;gBAAG,QAAQ;YAAE;YAChD,UAAU;gBAAE,SAAS;gBAAG,UAAU;gBAAG,QAAQ;YAAE;YAC/C,YAAY;gBAAE,SAAS;gBAAG,UAAU;gBAAG,QAAQ;YAAE;QACnD;IACF;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,WAAW;QACX,WAAW;QACX,UAAU;QACV,YAAY;QACZ,iBAAiB;IACnB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;qDAAc;oBAClB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;gCAAG,EAAE;IAEL,8DAA8D;IAC9D,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACvC,aAAa;YAEb,uDAAuD;YACvD,MAAM;mFAAoB;oBACxB,MAAM,YAAY;oBAClB,MAAM,sBAAsB;wBAAC;wBAAK;wBAAM;wBAAK;wBAAK;wBAAK;wBAAK;wBAAK;wBAAM;wBAAM;wBAAM;wBAAM;qBAAI;oBAC7F,OAAO,oBAAoB,GAAG;2FAAC,CAAC,YAAY;4BAC1C,MAAM,kBAAkB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAChD,OAAO,KAAK,KAAK,CAAC,YAAY,aAAa,CAAC,IAAI,eAAe;wBACjE;;gBACF;;YAEA,0CAA0C;YAC1C,MAAM;kFAAmB;oBACvB,MAAM,WAAW;oBACjB,MAAM,gBAAgB;wBAAC;wBAAK;wBAAK;wBAAK;wBAAK;wBAAK;wBAAK;qBAAI,CAAC,kBAAkB;;oBAC5E,OAAO,cAAc,GAAG;0FAAC,CAAA;4BACvB,MAAM,kBAAkB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAChD,OAAO,KAAK,KAAK,CAAC,WAAW,aAAa,CAAC,IAAI,eAAe;wBAChE;;gBACF;;YAEA,sCAAsC;YACtC,MAAM;sFAAuB;oBAC3B,MAAM,aAAa;wBACjB;4BAAE,MAAM;4BAAa,OAAO;4BAAI,OAAO;wBAAU;wBACjD;4BAAE,MAAM;4BAAU,OAAO;4BAAI,OAAO;wBAAU;wBAC9C;4BAAE,MAAM;4BAAa,OAAO;4BAAI,OAAO;wBAAU;wBACjD;4BAAE,MAAM;4BAAiB,OAAO;4BAAI,OAAO;wBAAU;wBACrD;4BAAE,MAAM;4BAAU,OAAO;4BAAG,OAAO;wBAAU;qBAC9C;oBACD,OAAO,WAAW,GAAG;8FAAC,CAAA,MAAO,CAAC;gCAC5B,GAAG,GAAG;gCACN,OAAO,IAAI,KAAK,GAAG,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BACxD,CAAC;;gBACH;;YAEA,6CAA6C;YAC7C,MAAM;mFAAoB;oBACxB,MAAM,SAAS;wBAAC;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;qBAAM;oBACnG,OAAO,OAAO,GAAG;2FAAC,CAAC,OAAO;4BACxB,MAAM,QAAQ,QAAS,QAAQ,OAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;4BAClE,MAAM,OAAO,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;4BAC/C,MAAM,SAAS,QAAQ,MAAM,OAAO;4BACpC,OAAO;gCAAE;gCAAO;gCAAO;gCAAM;4BAAO;wBACtC;;gBACF;;YAEA,iDAAiD;YACjD,MAAM;6FAA8B,CAAC,WAAqB;oBACxD,MAAM,iBAAiB,UAAU,MAAM;oHAAC,CAAC,GAAG,IAAM,IAAI;mHAAG;oBACzD,MAAM,kBAAkB,iBAAiB,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;oBAEpE,OAAO;wBACL,SAAS;4BACP,SAAS;4BACT,UAAU;4BACV,QAAQ,AAAC,CAAC,iBAAiB,eAAe,IAAI,kBAAmB;wBACnE;wBACA,WAAW;4BACT,SAAS,aAAa,UAAU;4BAChC,UAAU,KAAK,KAAK,CAAC,aAAa,UAAU,GAAG,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG;4BACzE,QAAQ,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAC7C;wBACA,UAAU;4BACR,SAAS,aAAa,aAAa;4BACnC,UAAU,KAAK,KAAK,CAAC,aAAa,aAAa,GAAG,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;4BAC7E,QAAQ,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAC7C;wBACA,YAAY;4BACV,SAAS,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;4BACzC,UAAU,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;4BAC1C,QAAQ,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAC7C;oBACF;gBACF;;YAEA,sDAAsD;YACtD;iEAAW;oBACT,MAAM,YAAY;oBAClB,MAAM,WAAW;oBACjB,MAAM,eAAe;oBACrB,MAAM,YAAY;oBAClB,MAAM,qBAAqB,4BAA4B,WAAW;oBAElE,aAAa;wBACX;wBACA;wBACA;wBACA;wBACA;oBACF;oBAEA,eAAe,IAAI;oBACnB,aAAa;gBACf;gEAAG;QACL;wDAAG;QAAC;KAAM;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG;QAAC;QAAsB,QAAQ,SAAS;KAAC;IAE5C,mDAAmD;IACnD,MAAM,gBAAgB,IAAM,CAAC;YAC3B,iBAAiB,kBAAkB,SAAS,YAAY;YACxD,WAAW;gBACT,OAAO,kBAAkB,SAAS,YAAY;gBAC9C,YAAY;YACd;YACA,MAAM;gBACJ,aAAa,kBAAkB,SAAS,YAAY;YACtD;QACF,CAAC;IAED,8CAA8C;IAC9C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE,IAAM,CAAC;gBACtC,GAAG,eAAe;gBAClB,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,UAAU;wBACV,YAAY;wBACZ,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,MAAM;oBACN,KAAK;gBACP;gBACA,SAAS;oBACP,SAAS;oBACT,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,aAAa,kBAAkB,SAAS,YAAY;oBACpD,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,SAAS;iEAAE,CAAC;4BACV,MAAM,OAAO,MAAM,CAAC,EAAE;4BACtB,OAAO,CAAC;;gEAEgD,EAAE,KAAK,IAAI,CAAC;;iEAEX,EAAE,KAAK,KAAK,CAAC;wBACtD,EAAE,KAAK,KAAK,CAAC,cAAc,GAAG;;;cAGxC,EAAE,KAAK,KAAK,GAAG,CAAC,UAAU,SAAS,CAAC,KAAK,SAAS,GAAG,EAAE,IAAI,CAAC,IAAI,iBAAiB,eAAe;;;QAGtG,CAAC;wBACH;;gBACF;gBACA,QAAQ;oBACN,MAAM;oBACN,KAAK;oBACL,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,MAAM;wBAAC;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;wBAAO;qBAAM;oBAC1F,UAAU;wBACR,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,UAAU;oBACZ;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,WAAW;wBACX,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,UAAU;oBACZ;oBACA,UAAU;wBACR,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;4BAC9C,MAAM;wBACR;oBACF;gBACF;gBACA,QAAQ;oBACN;wBACE,MAAM;wBACN,MAAM,UAAU,SAAS;wBACzB,MAAM,QAAQ,SAAS;wBACvB,QAAQ;wBACR,WAAW;4BACT,OAAO;4BACP,OAAO;wBACT;wBACA,WAAW;4BACT,OAAO;4BACP,cAAc,QAAQ,SAAS,KAAK,QAAQ;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE,GAAG;wBAC7D;wBACA,WAAW,QAAQ,SAAS,KAAK,SAAS;4BACxC,OAAO;gCACL,MAAM;gCACN,GAAG;gCACH,GAAG;gCACH,IAAI;gCACJ,IAAI;gCACJ,YAAY;oCACV;wCAAE,QAAQ;wCAAG,OAAO;oCAAyB;oCAC7C;wCAAE,QAAQ;wCAAG,OAAO;oCAA0B;iCAC/C;4BACH;wBACF,IAAI;wBACJ,UAAU;4BACR,OAAO;4BACP,WAAW;gCACT,YAAY;gCACZ,aAAa;4BACf;wBACF;wBACA,WAAW;4BACT,MAAM;gCACJ;oCAAE,MAAM;oCAAO,MAAM;gCAAM;gCAC3B;oCAAE,MAAM;oCAAO,MAAM;gCAAM;6BAC5B;4BACD,WAAW;gCACT,OAAO;4BACT;wBACF;wBACA,UAAU,QAAQ,UAAU,GAAG;4BAC7B,MAAM;gCACJ;oCAAE,MAAM;oCAAW,MAAM;gCAAU;6BACpC;4BACD,WAAW;gCACT,OAAO;gCACP,MAAM;4BACR;wBACF,IAAI;oBACN;iBACD;gBACD,MAAM;oBACJ,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,KAAK;oBACL,cAAc;gBAChB;gBACA,UAAU;oBACR;wBACE,MAAM;wBACN,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,YAAY;wBACZ,YAAY;wBACZ,aAAa;4BACX,OAAO;4BACP,YAAY;4BACZ,aAAa;4BACb,eAAe;4BACf,eAAe;wBACjB;oBACF;iBACD;gBACD,SAAS;oBACP,SAAS;wBACP,UAAU;4BACR,YAAY;wBACd;wBACA,SAAS,CAAC;wBACV,aAAa;4BACX,YAAY;wBACd;oBACF;oBACA,WAAW;wBACT,aAAa,kBAAkB,SAAS,YAAY;oBACtD;gBACF;YACF,CAAC;gDAAG;QAAC,UAAU,SAAS;QAAE,QAAQ,SAAS;QAAE;KAAc;IAE3D,6CAA6C;IAC7C,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM,CAAC;gBACrC,GAAG,eAAe;gBAClB,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,UAAU;wBACV,YAAY;wBACZ,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,MAAM;oBACN,KAAK;gBACP;gBACA,SAAS;oBACP,SAAS;oBACT,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,aAAa,kBAAkB,SAAS,YAAY;oBACpD,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,SAAS;gEAAE,CAAC;4BACV,MAAM,OAAO,MAAM,CAAC,EAAE;4BACtB,MAAM,aAAa,CAAC,AAAC,KAAK,KAAK,GAAG,UAAU,QAAQ,CAAC,MAAM;mFAAC,CAAC,GAAW,IAAc,IAAI;kFAAG,KAAM,GAAG,EAAE,OAAO,CAAC;4BAChH,OAAO,CAAC;;gEAEgD,EAAE,KAAK,IAAI,CAAC;;iEAEX,EAAE,KAAK,KAAK,CAAC;2BACnD,EAAE,KAAK,KAAK,CAAC,cAAc,GAAG;;;cAG3C,EAAE,WAAW;;;QAGnB,CAAC;wBACH;;gBACF;gBACA,QAAQ;oBACN,MAAM;oBACN,KAAK;oBACL,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,MAAM;wBAAC;wBAAU;wBAAW;wBAAa;wBAAY;wBAAU;wBAAY;qBAAS;oBACpF,UAAU;wBACR,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,UAAU;wBACV,QAAQ;oBACV;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,WAAW;wBACX,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,UAAU;oBACZ;oBACA,UAAU;wBACR,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;wBAChD;oBACF;oBACA,WAAW;wBACT,WAAW;4BACT,OAAO,kBAAkB,SAAS,YAAY;4BAC9C,MAAM;wBACR;oBACF;gBACF;gBACA,QAAQ;oBACN;wBACE,MAAM;wBACN,MAAM,UAAU,QAAQ;wBACxB,MAAM;wBACN,WAAW;4BACT,OAAO;gCACL,MAAM;gCACN,GAAG;gCACH,GAAG;gCACH,IAAI;gCACJ,IAAI;gCACJ,YAAY;oCACV;wCAAE,QAAQ;wCAAG,OAAO;oCAAU;oCAC9B;wCAAE,QAAQ;wCAAG,OAAO;oCAAU;iCAC/B;4BACH;4BACA,cAAc;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE;wBAC5B;wBACA,UAAU;4BACR,OAAO;4BACP,WAAW;gCACT,OAAO;gCACP,YAAY;gCACZ,aAAa;4BACf;wBACF;wBACA,WAAW;4BACT,MAAM;gCACJ;oCAAE,MAAM;oCAAO,MAAM;gCAAW;gCAChC;oCAAE,MAAM;oCAAO,MAAM;gCAAU;6BAChC;4BACD,WAAW;gCACT,OAAO;4BACT;wBACF;oBACF;iBACD;gBACD,MAAM;oBACJ,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,KAAK;oBACL,cAAc;gBAChB;gBACA,SAAS;oBACP,SAAS;wBACP,UAAU;4BACR,YAAY;wBACd;wBACA,SAAS,CAAC;wBACV,aAAa;4BACX,YAAY;wBACd;oBACF;oBACA,WAAW;wBACT,aAAa,kBAAkB,SAAS,YAAY;oBACtD;gBACF;YACF,CAAC;+CAAG;QAAC,UAAU,QAAQ;QAAE;KAAc;IAEvC,wCAAwC;IACxC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE,IAAM,CAAC;gBACzC,GAAG,eAAe;gBAClB,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,UAAU;wBACV,YAAY;wBACZ,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,MAAM;oBACN,KAAK;gBACP;gBACA,SAAS;oBACP,SAAS;oBACT,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,aAAa,kBAAkB,SAAS,YAAY;oBACpD,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,SAAS;oEAAE,CAAC;4BACV,OAAO,CAAC;;gEAEgD,EAAE,OAAO,IAAI,CAAC;;iEAEb,EAAE,OAAO,KAAK,CAAC;qBAC3D,EAAE,OAAO,KAAK,CAAC;;;cAGtB,EAAE,OAAO,OAAO,CAAC;;;QAGvB,CAAC;wBACH;;gBACF;gBACA,QAAQ;oBACN,QAAQ;oBACR,QAAQ;oBACR,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,UAAU;oBACZ;gBACF;gBACA,MAAM;oBACJ,KAAK;oBACL,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,cAAc;gBAChB;gBACA,QAAQ;oBACN;wBACE,MAAM;wBACN,MAAM;wBACN,QAAQ;4BAAC;4BAAO;yBAAM;wBACtB,QAAQ;4BAAC;4BAAO;yBAAM;wBACtB,mBAAmB;wBACnB,WAAW;4BACT,cAAc;4BACd,aAAa,kBAAkB,SAAS,YAAY;4BACpD,aAAa;wBACf;wBACA,OAAO;4BACL,MAAM;4BACN,UAAU;4BACV,WAAW;4BACX,OAAO,kBAAkB,SAAS,YAAY;4BAC9C,UAAU;4BACV,YAAY;4BACZ,qBAAqB,EAAE,gCAAgC;wBACzD;wBACA,UAAU;4BACR,WAAW;gCACT,YAAY;gCACZ,eAAe;gCACf,aAAa;4BACf;4BACA,OAAO;gCACL,MAAM;gCACN,UAAU;gCACV,YAAY;4BACd;wBACF;wBACA,WAAW;4BACT,MAAM;4BACN,QAAQ;4BACR,SAAS;4BACT,WAAW;gCACT,OAAO,kBAAkB,SAAS,YAAY;gCAC9C,OAAO;4BACT;wBACF;wBACA,MAAM,UAAU,YAAY;oBAC9B;iBACD;YACH,CAAC;mDAAG;QAAC,UAAU,YAAY;QAAE;KAAc;IAE3C,mDAAmD;IACnD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE;YACjC,MAAM,QAAQ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAG;iEAAG,CAAC,GAAG,IAAM,GAAG,EAAE,GAAG,CAAC;;YAC5D,MAAM,OAAO;gBAAC;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;aAAM;YAC9D,MAAM,cAAc,EAAE;YAEtB,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;gBAChC,IAAK,IAAI,OAAO,GAAG,OAAO,IAAI,OAAQ;oBACpC,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;oBAChD,YAAY,IAAI,CAAC;wBAAC;wBAAM;wBAAK;qBAAM;gBACrC;YACF;YAEA,OAAO;gBACL,GAAG,eAAe;gBAClB,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,UAAU;wBACV,YAAY;wBACZ,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,MAAM;oBACN,KAAK;gBACP;gBACA,SAAS;oBACP,UAAU;oBACV,iBAAiB,kBAAkB,SAAS,YAAY;oBACxD,aAAa,kBAAkB,SAAS,YAAY;oBACpD,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,SAAS;mEAAE,CAAC;4BACV,OAAO,CAAC;;kEAEgD,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;;mEAE/C,EAAE,OAAO,KAAK,CAAC;gCAClD,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;;;UAGvC,CAAC;wBACH;;gBACF;gBACA,MAAM;oBACJ,QAAQ;oBACR,KAAK;gBACP;gBACA,OAAO;oBACL,MAAM;oBACN,MAAM;oBACN,WAAW;wBACT,MAAM;oBACR;oBACA,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,UAAU;oBACZ;gBACF;gBACA,OAAO;oBACL,MAAM;oBACN,MAAM;oBACN,WAAW;wBACT,MAAM;oBACR;oBACA,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;wBAC9C,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,KAAK;oBACL,KAAK;oBACL,YAAY;oBACZ,QAAQ;oBACR,MAAM;oBACN,QAAQ;oBACR,SAAS;wBACP,OAAO;4BAAC;4BAAW;4BAAW;4BAAW;4BAAW;4BAAW;4BAAW;4BAAW;4BAAW;4BAAW;4BAAW;yBAAU;oBAClI;oBACA,WAAW;wBACT,OAAO,kBAAkB,SAAS,YAAY;oBAChD;gBACF;gBACA,QAAQ;oBAAC;wBACP,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,OAAO;4BACL,MAAM;wBACR;wBACA,UAAU;4BACR,WAAW;gCACT,YAAY;gCACZ,aAAa;4BACf;wBACF;oBACF;iBAAE;YACJ;QACF;kDAAG;QAAC;KAAc;IAElB,sCAAsC;IACtC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE,IAAM,CAAC;gBACtC,GAAG,eAAe;gBAClB,OAAO;oBACL,MAAM;oBACN,WAAW;wBACT,UAAU;wBACV,YAAY;wBACZ,OAAO,kBAAkB,SAAS,YAAY;oBAChD;oBACA,MAAM;oBACN,KAAK;gBACP;gBACA,QAAQ;oBACN;wBACE,MAAM;wBACN,MAAM;wBACN,QAAQ;4BAAC;4BAAO;yBAAM;wBACtB,YAAY;wBACZ,UAAU,CAAC;wBACX,KAAK;wBACL,KAAK;wBACL,aAAa;wBACb,WAAW;4BACT,OAAO;wBACT;wBACA,UAAU;4BACR,MAAM;4BACN,OAAO;wBACT;wBACA,SAAS;4BACP,MAAM;wBACR;wBACA,UAAU;4BACR,WAAW;gCACT,OAAO;gCACP,OAAO;oCACL;wCAAC;wCAAK;qCAAU;oCAChB;wCAAC;wCAAK;qCAAU;oCAChB;wCAAC;wCAAG;qCAAU;iCACf;4BACH;wBACF;wBACA,UAAU;4BACR,UAAU,CAAC;4BACX,aAAa;4BACb,WAAW;gCACT,OAAO;gCACP,OAAO,kBAAkB,SAAS,YAAY;4BAChD;wBACF;wBACA,WAAW;4BACT,UAAU,CAAC;4BACX,QAAQ;4BACR,WAAW;gCACT,OAAO;gCACP,OAAO,kBAAkB,SAAS,YAAY;4BAChD;wBACF;wBACA,WAAW;4BACT,UAAU,CAAC;4BACX,OAAO,kBAAkB,SAAS,YAAY;4BAC9C,UAAU;wBACZ;wBACA,QAAQ;4BACN,MAAM;wBACR;wBACA,OAAO;4BACL,MAAM;wBACR;wBACA,QAAQ;4BACN,gBAAgB;4BAChB,OAAO;4BACP,YAAY;4BACZ,cAAc;4BACd,cAAc;gCAAC;gCAAG;6BAAO;4BACzB,UAAU;4BACV,YAAY;4BACZ,WAAW;4BACX,OAAO,kBAAkB,SAAS,YAAY;wBAChD;wBACA,MAAM;4BACJ;gCACE,OAAO,UAAU,kBAAkB,CAAC,UAAU,CAAC,OAAO;gCACtD,MAAM;4BACR;yBACD;oBACH;iBACD;YACH,CAAC;gDAAG;QAAC,UAAU,kBAAkB,CAAC,UAAU,CAAC,OAAO;QAAE;KAAc;IAEpE,kDAAkD;IAClD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAE;YACvB,MAAM,eAAe,UAAU,SAAS,CAAC,MAAM;8DAAC,CAAC,GAAW,IAAc,IAAI;6DAAG;YACjF,MAAM,oBAAoB,eAAe,UAAU,SAAS,CAAC,MAAM;YACnE,MAAM,YAAY,UAAU,QAAQ,CAAC,MAAM;2DAAC,CAAC,GAAW,IAAc,IAAI;0DAAG;YAC7E,MAAM,eAAe,YAAY,UAAU,QAAQ,CAAC,MAAM;YAE1D,OAAO;gBACL;oBACE,OAAO;oBACP,OAAO,MAAM,aAAa,cAAc;oBACxC,MAAM,qNAAA,CAAA,aAAU;oBAChB,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,aAAa;oBACb,OAAO;oBACP,UAAU,CAAC,MAAM,EAAE,kBAAkB,cAAc,GAAG,MAAM,CAAC;gBAC/D;gBACA;oBACE,OAAO;oBACP,OAAO,UAAU,kBAAkB,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ;oBAC9D,MAAM,uMAAA,CAAA,QAAK;oBACX,OAAO;oBACP,SAAS;oBACT,QAAQ,GAAG,UAAU,kBAAkB,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,MAAM,KAAK,UAAU,kBAAkB,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBACrI,aAAa,UAAU,kBAAkB,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,uCAAuC;oBACxG,OAAO,UAAU,kBAAkB,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,OAAO;oBAClE,UAAU;gBACZ;gBACA;oBACE,OAAO;oBACP,OAAO,UAAU,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ;oBAC7D,MAAM,2MAAA,CAAA,UAAO;oBACb,OAAO;oBACP,SAAS;oBACT,QAAQ,GAAG,UAAU,kBAAkB,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,MAAM,KAAK,UAAU,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBACnI,aAAa,UAAU,kBAAkB,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,uCAAuC;oBACvG,OAAO,UAAU,kBAAkB,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,OAAO;oBACjE,UAAU;gBACZ;gBACA;oBACE,OAAO;oBACP,OAAO,GAAG,UAAU,kBAAkB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC5D,MAAM,yMAAA,CAAA,SAAM;oBACZ,OAAO;oBACP,SAAS;oBACT,QAAQ,GAAG,UAAU,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,MAAM,KAAK,UAAU,kBAAkB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBACvI,aAAa,UAAU,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,uCAAuC;oBACzG,OAAO,UAAU,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,OAAO;oBACnE,UAAU;gBACZ;gBACA;oBACE,OAAO;oBACP,OAAO,MAAM,aAAa,cAAc;oBACxC,MAAM,yNAAA,CAAA,eAAY;oBAClB,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,aAAa;oBACb,OAAO;oBACP,UAAU;gBACZ;gBACA;oBACE,OAAO;oBACP,OAAO,GAAG,CAAC,AAAC,eAAe,CAAC,eAAe,IAAI,IAAK,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;oBAC5E,MAAM,qNAAA,CAAA,aAAU;oBAChB,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,aAAa;oBACb,OAAO;oBACP,UAAU;gBACZ;aACD;QACH;wCAAG;QAAC;QAAW,MAAM,eAAe;KAAC;IAErC,4BAA4B;IAC5B,MAAM,iBAAiB,kBACrB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;0CAGzE,6LAAC;gCACC,OAAO,QAAQ,SAAS;gCACxB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAA+B,CAAC;gCACzG,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;0CAGvB,6LAAC;gCACC,OAAO,QAAQ,SAAS;gCACxB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAA+B,CAAC;gCACzG,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;;;;;;;kCAIzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,iBAAiB,IAAI;;;;;;kDAClE,6LAAC;kDAAM,YAAY,gBAAgB;;;;;;;;;;;;0CAGrC,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOhB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;;;;0BAGD,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,KAAK,sBAClB,6LAAC;wBAAgB,WAAU;kCACzB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,eAAe,EAAE,IAAI,OAAO,CAAC,wDAAwD,CAAC;sDACrG,cAAA,6LAAC,IAAI,IAAI;gDAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,KAAK,EAAE;;;;;;;;;;;sDAE7C,6LAAC;4CAAI,WAAU;;gDACZ,IAAI,KAAK,KAAK,sBAAQ,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDACzC,IAAI,KAAK,KAAK,wBAAU,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAC7C,IAAI,KAAK,KAAK,2BAAa,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAIjD,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDACV,IAAI,KAAK;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDACV,IAAI,KAAK;;;;;;wCAEX,IAAI,QAAQ,kBACX,6LAAC;4CAAE,WAAU;sDACV,IAAI,QAAQ;;;;;;sDAGjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAW,CAAC,oBAAoB,EAAE,IAAI,WAAW,EAAE;8DACtD,IAAI,MAAM;;;;;;8DAEb,6LAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;;uBA7BzD;;;;;;;;;;0BAwCd,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;4BAIhE,0BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;qDAI5D,6LAAC,0JAAA,CAAA,UAAY;gCACX,QAAQ;gCACR,OAAO;oCAAE,QAAQ,WAAW,UAAU;gCAAQ;;;;;;;;;;;;kCAMpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;4BAIhE,0BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;qDAI5D,6LAAC,0JAAA,CAAA,UAAY;gCACX,QAAQ;gCACR,OAAO;oCAAE,QAAQ,WAAW,UAAU;gCAAQ;;;;;;;;;;;;;;;;;;0BAOtD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA2E;;;;;;sDAG3F,6LAAC;4CAAI,WAAU;sDACZ;gDACC;oDAAE,IAAI;oDAAO,OAAO;oDAAkB,OAAO;oDAAe,YAAY;gDAAoB;gDAC5F;oDAAE,IAAI;oDAAa,OAAO;oDAAa,OAAO;oDAAgB,YAAY;gDAAqB;gDAC/F;oDAAE,IAAI;oDAAU,OAAO;oDAAU,OAAO;oDAAe,YAAY;gDAAoB;gDACvF;oDAAE,IAAI;oDAAa,OAAO;oDAAa,OAAO;oDAAgB,YAAY;gDAAqB;gDAC/F;oDAAE,IAAI;oDAAiB,OAAO;oDAAiB,OAAO;oDAAc,YAAY;gDAAmB;gDACnG;oDAAE,IAAI;oDAAU,OAAO;oDAAU,OAAO;oDAAiB,YAAY;gDAAsB;6CAC5F,CAAC,GAAG,CAAC,CAAC,yBACL,6LAAC;oDAEC,SAAS,IAAM,oBAAoB,SAAS,EAAE;oDAC9C,WAAW,CAAC;;;sBAGV,EAAE,qBAAqB,SAAS,EAAE,GAC9B,GAAG,SAAS,KAAK,CAAC,4EAA4E,EAAE,SAAS,UAAU,EAAE,GACrH,4JACH;oBACH,CAAC;;sEAED,6LAAC;4DACC,WAAW,CAAC,wDAAwD,EAClE,qBAAqB,SAAS,EAAE,GAAG,gBAAgB,SAAS,KAAK,EACjE;;;;;;sEAEJ,6LAAC;4DAAK,WAAU;sEAAqB,SAAS,KAAK;;;;;;;mDAhB9C,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;4BAsBzB,0BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;qDAI5D,6LAAC,0JAAA,CAAA,UAAY;gCACX,QAAQ;gCACR,OAAO;oCAAE,QAAQ,WAAW,UAAU;gCAAQ;;;;;;;;;;;;kCAMpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;;;;;;4BAIhE,0BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;qDAI5D,6LAAC,0JAAA,CAAA,UAAY;gCACX,QAAQ;gCACR,OAAO;oCAAE,QAAQ,WAAW,UAAU;gCAAQ;;;;;;;;;;;;;;;;;;0BAOtD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAG5D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;oBAIhE,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;6CAI5D,6LAAC,0JAAA,CAAA,UAAY;wBACX,QAAQ;wBACR,OAAO;4BAAE,QAAQ,WAAW,UAAU;wBAAQ;;;;;;;;;;;;0BAMpD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAuD;;;;;;;;;;;;;;;;;;0CAG3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAK,WAAU;kEAAuD;;;;;;;;;;;;0DAEzE,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;0DAGxD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAA+B,OAAO;wDAAE,OAAO;oDAAM;;;;;;;;;;;;;;;;;kDAIxE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAyD;;;;;;;;;;;;0DAE3E,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;0DAG1D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAgC,OAAO;wDAAE,OAAO;oDAAM;;;;;;;;;;;;;;;;;kDAIzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEAA2D;;;;;;;;;;;;0DAE7E,6LAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAiC,OAAO;wDAAE,OAAO;oDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM9E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;;;;;;;0CAG7E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC;wDAAK,WAAU;kEAA6C;;;;;;;;;;;;;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;kDAG/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,YAAY,kBAAkB;;;;;;;;;;;;;;;;;kDAIrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOvE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAuD;;;;;;;;;;;;;;;;;;0CAG3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAgC,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;kEAEvE,6LAAC;wDAAK,WAAU;kEAA6C;;;;;;;;;;;;;;;;;;kDAGjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA+B,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;kEAEtE,6LAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAG/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAiC,OAAO;gEAAE,OAAO;4DAAM;;;;;;;;;;;kEAExE,6LAAC;wDAAK,WAAU;kEAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAG5D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;kCAKjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;kDAE3E,6LAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;0CAK5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAuD;;;;;;;;;;;;kDAEzE,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAK1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAK,WAAU;0DAA2D;;;;;;;;;;;;kDAE7E,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;;;;;;;0CAK9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAA2D;;;;;;;;;;;;kDAE7E,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;;;;;;;;;;;;;kCAMhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;0CAEzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzE;GAj2CwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}]}